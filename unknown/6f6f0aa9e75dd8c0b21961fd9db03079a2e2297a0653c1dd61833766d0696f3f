<p class="page-title">{{ kbLabels.pageTitle }}</p>
<div class="create-knowledge-base-container">
  <form [formGroup]="knowledgeBaseForm">
    <div class="form-layout">
      <!-- Left Column -->
      <div class="left-column" [class.collapsed]="isLeftCollapsed">
        <div class="left-header">
          <span class="left-title" *ngIf="!isLeftCollapsed">Knowledge Base Description</span>
          <lucide-icon name="panel-left" class="collapse-icon" (click)="toggleLeftPanel()"></lucide-icon>
        </div>
        <ava-popup messageAlignment="center" [show]="showInfoPopup" title="" message="{{submissionMessage}}"
          [showHeaderIcon]="true" headerIconName="{{iconName}}" iconColor="{{iconColor}}" [showClose]="true"
          (closed)="handlePopupClose()">
        </ava-popup>
        <!-- Knowledge Base Details Card -->
        <div class="card-content" *ngIf="!isLeftCollapsed">
          <ava-textbox id="knowledgeBaseName" placeholder="{{kbLabels.placeholderKnowledgeBase}}"
            label="{{kbLabels.knowledgeBaseName}}" formControlName="name" [control]="getControl('name')"
            [error]="getFieldError('name')" size="md" [fullWidth]="true" [required]="true">
          </ava-textbox>
          <ava-textarea label="{{kbLabels.description}}" placeholder="{{kbLabels.placeholderDescription}}"
            formControlName="description" [control]="getControl('description')" [error]="getFieldError('description')"
            size="md" [fullWidth]="true" [required]="true"></ava-textarea>
        </div>
      </div>
      <!-- Right Column -->
      <div class="right-column">
        <div class="card-content">
          <div class="section">
            <div class="knowledge-base-head">
              <div class="knowledge-base-title">
                {{kbLabels.knowledgeBaseConfiguration}}
              </div>
              <!-- Buttons at bottom of right column -->
              <div class="right-column-buttons">
                <!-- <ava-button label="Exit" (userClick)="onExit()" variant="secondary" size="medium" state="default">
                  </ava-button>  [disabled]="isSubmitDisabled()" -->
                <ava-button label="{{saveOrUpdateLabel}}" (userClick)="onSave()" variant="primary" size="medium"
                  [customStyles]="{
                  background:
                    'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
                  '--button-effect-color': '33, 90, 214',
                }" state="default" [disabled]="isSubmitDisabled()">
                </ava-button>
              </div>
            </div>
          </div>

          <!-- Retriever Selection -->
          <ng-container *ngIf="!isEditMode">
            <div class="section">
              <h3 class="section-title">{{kbLabels.selectRetriever}}</h3>
              <div class="retriever-options">
                <ava-button *ngFor="let retriever of retrieverOptions" [label]="retriever" size="small"
                  [variant]="selectedRetriever === retriever ? 'primary' : 'secondary'"
                  [state]="selectedRetriever === retriever ? 'active' : 'default'" [disabled]="isEditMode"
                  [customStyles]="
                    selectedRetriever === retriever
                      ? {
                          background: 'linear-gradient(103.35deg, #215AD6 31.33%, #03BDD4 100%)',
                          '--button-effect-color': '33, 90, 214',
                          color: '#fff',
                          border: 'none'
                        }
                      : {}
                  " (userClick)="selectRetriever(retriever)">
                </ava-button>
              </div>
            </div>
            <!-- Split Size Configuration -->
            <div class="section" *ngIf="selectedRetriever === 'Default'">
              <h3 class="section-title">{{kbLabels.splitSize}}</h3>
              <div class="split-size-container">
                <ava-slider [disabled]="isEditMode" [value]="splitSize" [min]="100" [max]="20000" [step]="1"
                  [showTooltip]="false" (valueChange)="splitSizeChange($event)" formControlName="splitSize">
                </ava-slider>
                <div class="value-box">
                  <ava-textbox type="number" (change)="onSliderInputChange($event, 'splitSize')"
                    formControlName="splitSize"></ava-textbox>
                </div>
              </div>
              <!-- Embedding Model Selection Dropdown for Default retriever -->
            </div>
            <!-- Parent Doc Split Size Configuration -->
            <div class="section" *ngIf="selectedRetriever === kbLabels.parentDoc">
              <div class="split-section">
                <h3 class="section-title">{{kbLabels.parentSplitSize}}</h3>
                <div class="split-size-container">
                  <ava-slider [disabled]="isEditMode" [value]="parentSplitSize" [min]="100" [max]="20000" [step]="1"
                    [showTooltip]="false" (valueChange)="onParentSplitSizeChange($event)"
                    formControlName="parentSplitSize">
                  </ava-slider>
                  <div class="value-box">
                    <ava-textbox type="number" (change)="onSliderInputChange($event, 'parentSplitSize')"
                      formControlName="parentSplitSize"></ava-textbox>
                  </div>
                </div>
              </div>
              <div class="split-section">
                <h3 class="section-title">{{kbLabels.childSplitSize}}</h3>
                <div class="split-size-container">
                  <ava-slider [disabled]="isEditMode" [value]="childSplitSize" [min]="100" [max]="20000" [step]="1"
                    [showTooltip]="false" (valueChange)="onChildSplitSizeChange($event)"
                    formControlName="childSplitSize">
                  </ava-slider>
                  <div class="value-box">
                    <ava-textbox type="number" (change)="onSliderInputChange($event, 'childSplitSize')"
                      formControlName="childSplitSize"></ava-textbox>
                  </div>
                </div>
                <div *ngIf="selectedRetriever === kbLabels.parentDoc && parentSplitSize <= childSplitSize"
                  class="field-error">
                  {{kbLabels.parentChildSplitValidation}}
                </div>
              </div>
            </div>
            <ava-dropdown class="dropdown-medium" dropdownTitle="{{kbLabels.selectEmbeddingModel}}" id="embeddingModel"
              label="{{kbLabels.embeddingModel}}" [options]="embeddingModelOptions"
              [formControl]="getControl('embeddingModel')" [error]="getFieldError('embeddingModel')"
              [required]="true"></ava-dropdown>
          </ng-container>

          <div *ngIf="isEditMode">
            <ava-table [columns]="tableColumns" [data]="tableData">
            </ava-table>
          </div>
          <hr *ngIf="!isEditMode">
          <div class="section">
            <ava-dropdown class="dropdown-medium" label="{{kbLabels.uploadType}}" id="uploadType"
              formControlName="uploadType" [options]="uploadOptions" [selectedValue]="uploadPlaceholder"
              (valueChange)="onUploadTypeChange($event)" [error]="getFieldError('uploadType')" variant="primary"
              size="md" [fullWidth]="true" [required]="true">
            </ava-dropdown>
          </div>
          <ng-container *ngIf="selectedUploadType === 'upload-files'">
            <div class="ava-file-upload-wrapper">
              <ava-file-upload (filesListChanged)="filesListChanged($event)" [allowedFormats]="allowedFormats"
                [showUploadButton]="showUploadButton" [componentTitle]="componentTitle"
                [showDialogCloseIcon]="showDialogCloseIcon" [maxFileSize]="maxFileSize" uploaderId="light-uploader"
                [preview]="true">
              </ava-file-upload>
              <!-- Optional Error Display -->
            </div>
            <div *ngIf="hasZeroKbFile" class="field-error">
              {{kbLabels.zeroKbFileError}}
            </div>
          </ng-container>
          <div *ngIf="fileUploadRequired && uploadedFiles.length === 0" class="field-error">
            {{kbLabels.fileUploadIsRequired}}
          </div>

          <div *ngIf="selectedUploadType" class="upload-fields-grid">
            <!-- Azure Blob -->
            <ng-container *ngIf="selectedUploadType === 'azure-blob'">
              <h3>{{kbLabels.configSource}}</h3>
              <ava-textbox label="{{kbLabels.containerName}}" placeholder="{{kbLabels.placeholderContainerName}}"
                formControlName="containerName" [control]="getControl('containerName')"
                [error]="getFieldError('containerName')" [required]="true"></ava-textbox>
              <ava-textbox label="{{kbLabels.accountName}}" placeholder="{{kbLabels.placeholderAccountName}}"
                formControlName="accountName" [control]="getControl('accountName')"
                [error]="getFieldError('accountName')" [required]="true"></ava-textbox>
              <ava-textbox label="{{kbLabels.accountKey}}" placeholder="{{kbLabels.placeholderAccountKey}}"
                formControlName="accountKey" [control]="getControl('accountKey')" [error]="getFieldError('accountKey')"
                [required]="true"></ava-textbox>
            </ng-container>
            <!-- GitHub -->
            <ng-container *ngIf="selectedUploadType === 'github'">
              <h3>{{kbLabels.configSource}}</h3>
              <ava-textbox label="{{kbLabels.githubKey}}" formControlName="githubKey"
                placeholder="{{kbLabels.placeholderGithubKey}}" [control]="getControl('githubKey')"
                [error]="getFieldError('githubKey')" [required]="true"></ava-textbox>
              <ava-textbox label="{{kbLabels.githubAccount}}" placeholder="{{kbLabels.placeholderGithubAccount}}"
                formControlName="githubAccount" [control]="getControl('githubAccount')"
                [error]="getFieldError('githubAccount')" [required]="true"></ava-textbox>
              <ava-textbox label="{{kbLabels.githubRepository}}" formControlName="githubRepo"
                placeholder="{{kbLabels.placeholderGithubRepository}}" [control]="getControl('githubRepo')"
                [error]="getFieldError('githubRepo')" [required]="true"></ava-textbox>
              <ava-textbox label="{{kbLabels.githubBranch}}" placeholder="{{kbLabels.placeholderGithubBranch}}"
                formControlName="githubBranch" [control]="getControl('githubBranch')"
                [error]="getFieldError('githubBranch')" [required]="true"></ava-textbox>
            </ng-container>
            <!-- Share Point -->
            <ng-container *ngIf="selectedUploadType === 'share-point'">
              <h3>{{kbLabels.configSource}}</h3>
              <ava-textbox label="{{kbLabels.clientId}}" placeholder="{{kbLabels.placeholderClientId}}"
                formControlName="clientId" [control]="getControl('clientId')" [error]="getFieldError('clientId')"
                [required]="true"></ava-textbox>
              <ava-textbox label="{{kbLabels.clientSecret}}" placeholder="{{kbLabels.placeholderClientSecret}}"
                formControlName="clientSecret" [control]="getControl('clientSecret')"
                [error]="getFieldError('clientSecret')" [required]="true"></ava-textbox>
              <ava-textbox label="{{kbLabels.tenantId}}" placeholder="{{kbLabels.placeholderTenantId}}"
                formControlName="tenantId" [control]="getControl('tenantId')" [error]="getFieldError('tenantId')"
                [required]="true"></ava-textbox>
              <ava-textbox label="{{kbLabels.siteName}}" placeholder="{{kbLabels.placeholderSiteName}}"
                formControlName="sharepointSiteName" [control]="getControl('share pointSiteName')"
                [error]="getFieldError('share pointSiteName')" [required]="true"></ava-textbox>
              <ava-textbox label="{{kbLabels.folderPath}}" placeholder="{{kbLabels.placeholderFolderPath}}"
                formControlName="sharepointFolderPath" [control]="getControl('sharepointFolderPath')"
                [error]="getFieldError('sharepointFolderPath')" [required]="true"></ava-textbox>
            </ng-container>
            <!-- Confluence Wiki -->
            <ng-container *ngIf="selectedUploadType === 'confluence-wiki'">
              <h3>{{kbLabels.configSource}}</h3>
              <ava-textbox label="{{kbLabels.emailId}}" type="email" placeholder="{{kbLabels.placeholderEmailId}}"
                formControlName="email" [control]="getControl('email')" [error]="getFieldError('email')"
                [required]="true"></ava-textbox>
              <ava-textbox label="{{kbLabels.confluenceClientId}}"
                placeholder="{{kbLabels.placeholderConfluenceClientId}}" formControlName="clientId"
                [control]="getControl('clientId')" [error]="getFieldError('clientId')" [required]="true"></ava-textbox>
              <ava-textbox label="{{kbLabels.confluenceClientSecret}}"
                placeholder="{{kbLabels.placeholderConfluenceClientSecret}}" formControlName="clientSecret"
                [control]="getControl('clientSecret')" [error]="getFieldError('clientSecret')"
                [required]="true"></ava-textbox>
              <ava-textbox label="{{kbLabels.apiToken}}" placeholder="{{kbLabels.placeholderApiToken}}"
                formControlName="apiToken" [control]="getControl('apiToken')" [error]="getFieldError('apiToken')"
                [required]="true"></ava-textbox>
              <ava-textbox label="{{kbLabels.baseUrl}}" placeholder="{{kbLabels.placeholderBaseUrl}}"
                formControlName="baseUrl" [control]="getControl('baseUrl')" [error]="getFieldError('baseUrl')"
                [required]="true"></ava-textbox>
              <ava-textbox label="{{kbLabels.spaceKey}}" placeholder="{{kbLabels.placeholderSpaceKey}}"
                formControlName="spaceKey" [control]="getControl('spaceKey')" [error]="getFieldError('spaceKey')"
                [required]="true"></ava-textbox>
              <ava-textbox type="number" label="{{kbLabels.overlap}}" placeholder="{{kbLabels.placeholderOverlap}}"
                formControlName="overlap" [control]="getControl('overlap')" [error]="getFieldError('overlap')"
                [required]="true"></ava-textbox>
            </ng-container>
            <!-- Database -->
            <ng-container *ngIf="selectedUploadType === 'database'">
              <h3>{{kbLabels.configSource}}</h3>
              <div class="schema-dropdown">
                <ava-dropdown label="{{kbLabels.selectScheme}}" dropdownTitle="{{kbLabels.selectScheme}}"
                  [options]="schemeOptions" formControlName="scheme" [control]="getControl('scheme')"
                  [error]="getFieldError('scheme')" [required]="true">
                </ava-dropdown>
              </div>
              <ava-textbox label="{{kbLabels.dbHost}}" placeholder="{{kbLabels.placeholderHost}}" formControlName="host"
                [control]="getControl('host')" [error]="getFieldError('host')" [required]="true"></ava-textbox>
              <ava-textbox label="{{kbLabels.dbPort}}" placeholder="{{kbLabels.placeholderPort}}" formControlName="port"
                [control]="getControl('port')" [error]="getFieldError('port')" [required]="true"></ava-textbox>
              <ava-textbox label="{{kbLabels.dbUser}}" placeholder="{{kbLabels.placeholderUser}}" formControlName="user"
                [control]="getControl('user')" [error]="getFieldError('user')" [required]="true"></ava-textbox>
              <ava-textbox label="{{kbLabels.dbPassword}}" placeholder="{{kbLabels.placeholderPassword}}"
                formControlName="password" [control]="getControl('password')" [error]="getFieldError('password')"
                [required]="true"></ava-textbox>
              <ava-textbox label="{{kbLabels.dbName}}" placeholder="{{kbLabels.placeholderDbName}}"
                formControlName="dbname" [control]="getControl('dbname')" [error]="getFieldError('dbname')"
                [required]="true"></ava-textbox>
              <ava-textbox label="{{kbLabels.dbQuery}}" placeholder="{{kbLabels.placeholderDbQuery}}"
                formControlName="query" [control]="getControl('query')" [error]="getFieldError('query')"
                [required]="true"></ava-textbox>
              <ava-textbox type="number" label="{{kbLabels.overlap}}" placeholder="{{kbLabels.placeholderOverlap}}"
                formControlName="overlap" [control]="getControl('overlap')" [error]="getFieldError('overlap')"
                [required]="true"></ava-textbox>
            </ng-container>
          </div>
        </div>
      </div>
    </div>
  </form>
</div>