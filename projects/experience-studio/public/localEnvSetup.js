// localEnvSetup.js for Experience Studio
(function() {
  const isLocal = window.location.hostname === 'localhost';
  if (isLocal) {
    window.env = {
      production: 'false',
      baseUrl: 'https://aava-dev.avateam.io',
      experienceStudioUrl: 'http://localhost:4201/experience/',
      experienceBaseUrl: 'https://ava-plus-experience-studio-api-dev.azurewebsites.net',
      experienceApiUrl: 'https://ava-plus-experience-studio-api-dev.azurewebsites.net',
      apiUrl: 'https://ava-plus-experience-studio-api-dev.azurewebsites.net',
      elderWandUrl: 'http://localhost:4200',
      experianceApiAuthUrl: 'https://aava-dev.avateam.io/api/auth',
      experianceRedirectUrl: 'http://localhost:4201/experience/',
      consoleApiAuthUrl: 'https://aava-dev.avateam.io/api/auth'
    };
  }
})();
