// Helper function to safely get environment variables from window.env
const getRequiredEnv = (key: string): string => {
  interface EnvWindow extends Window { env?: Record<string, string>; }
  const envWindow = window as EnvWindow;
  const value = envWindow.env?.[key];
  if (value === undefined || value === null) {
    throw new Error(`Environment variable '${key}' is not defined in window.env.`);
  }
  return String(value);
};

const dynamicBaseUrl: string = getRequiredEnv('experienceBaseUrl');// experienceBaseUrl TODO: remove later to use new aavalink for server

export const environment = {
  production: false,
  elderWandUrl: getRequiredEnv('elderWandUrl'),
  apiUrl: getRequiredEnv('experienceBaseUrl'),// experienceBaseUrl TODO: remove later to use new aavalink for server
  experienceApiUrl: getRequiredEnv('experienceApiUrl'),
  experianceApiAuthUrl: getRequiredEnv('consoleApiAuthUrl'),
  experianceRedirectUrl: getRequiredEnv('experienceStudioUrl'),
  getApiUrl: (endpoint: string) => {
    const baseUrl = getRequiredEnv('experienceBaseUrl');// experienceBaseUrl TODO: remove later to use new aavalink for server
    return `${baseUrl}${endpoint}`;
  }
};
console.log(environment);
