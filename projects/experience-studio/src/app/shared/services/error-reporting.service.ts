import { Injectable, inject } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, throwError, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { createLogger } from '../utils/logger';
import { HttpErrorHelperService } from './error-handling/http-error-helper.service';
import { UserSignatureService } from './user-signature.service';
/**
 * Interface for error reporting request payload (LEGACY FORMAT - COMMENTED OUT)
 */
// export interface ErrorReportRequest {
//   error: string;
// }

/**
 * Interface for code file in error report request (matches code-generation format)
 * ENHANCEMENT: Updated to match code-generation.service.ts payload structure
 */
export interface CodeFileForErrorReport {
  fileName: string;
  content: string;
}

/**
 * Interface for error reporting request payload (NEW CODE-GENERATION FORMAT)
 * ENHANCEMENT: Updated to match /code-generation/regenerate/code endpoint format
 */
export interface ErrorReportRequest {
  prompt: string; // Stringified JSON containing code and user_request (error message)
  image: string[]; // Image array - empty array if no images
}

/**
 * Interface for error reporting response
 */
export interface ErrorReportResponse {
  success: boolean;
  message?: string;
  status?: string;
}

/**
 * Service for reporting build errors to the backend
 * Used by the retry mechanism when BUILD status is FAILED
 */
@Injectable({
  providedIn: 'root'
})
export class ErrorReportingService {
  private readonly http = inject(HttpClient);
  private readonly httpErrorHelper = inject(HttpErrorHelperService);

  private readonly apiUrl = environment.experienceApiUrl;
  private readonly errorEndpoint = '/code-generation/error/build';
  private readonly codeGenerationErrorEndpoint = '/code-generation/error/build';
  constructor(private userSignatureService: UserSignatureService){}
  /**
   * Report a build or deploy error to the backend
   * ENHANCEMENT: Updated to use code-generation payload format
   * @param projectId The project ID
   * @param statusId The status ID (job ID)
   * @param errorMessage The error message to report
   * @param codeFiles Optional code files from Monaco editor (matches code-generation format)
   * @returns Observable of the error report response
   */
  reportBuildError(
    projectId: string,
    statusId: string,
    errorMessage: string,
    codeFiles?: CodeFileForErrorReport[]
  ): Observable<ErrorReportResponse> {
    if (!projectId || !statusId) {

      return throwError(() => new Error('Missing required parameters: projectId and statusId are required'));
    }

    if (!errorMessage || errorMessage.trim() === '') {

      errorMessage = 'Build or deploy process failed. No specific error details available.';
    }

    // Set up query parameters
    const signature = this.userSignatureService.getUserSignatureSync();
    const params = new HttpParams()
      .set('user_signature',signature)
      .set('project_id', projectId)
      .set('request_id', statusId);

    // LEGACY PAYLOAD FORMAT (COMMENTED OUT FOR REFERENCE)
    // const payload: ErrorReportRequest = {
    //   error: errorMessage.trim()
    // };

    // ENHANCEMENT: Create payload using code-generation format
    // Create the prompt object that will be stringified (matches code-generation.service.ts format)
    const promptObject = {
      code: codeFiles && codeFiles.length > 0 ? codeFiles.map(file => ({
        fileName: file.fileName || 'unknown.txt',
        content: file.content || ''
      })) : [],
      user_request: errorMessage.trim() // Use error message as user_request
    };

    // Create the payload according to the code-generation format
    const payload: ErrorReportRequest = {
      prompt: JSON.stringify(promptObject), // Stringify the prompt object
      image: [] // Always include empty image array for error reports
    };

    return this.http.post<ErrorReportResponse>(
      this.apiUrl + this.errorEndpoint,
      payload,
      { params }
    ).pipe(
      map(response => {

        return {
          success: true,
          message: response.message || 'Error reported successfully',
          status: response.status || 'success'
        };
      }),
      catchError(error => {

        // Return a successful response even when error reporting fails
        // This allows the retry mechanism to continue smoothly
        return of({
          success: false,
          message: 'Failed to report build/deploy error. Retry will continue without error reporting.',
          status: 'error'
        });
      })
    );
  }

  /**
   * Report build or deploy error with automatic error message extraction from polling response
   * ENHANCEMENT: Updated to support code files from Monaco editor
   * @param projectId The project ID
   * @param statusId The status ID (job ID)
   * @param pollingResponse The last polling response containing error details
   * @param codeFiles Optional code files from Monaco editor (matches code-generation format)
   * @returns Observable of the error report response
   */
  reportBuildErrorFromPollingResponse(
    projectId: string,
    statusId: string,
    pollingResponse: any,
    codeFiles?: CodeFileForErrorReport[]
  ): Observable<ErrorReportResponse> {
    let errorMessage = '';

    try {
      // Extract error message from polling response
      if (pollingResponse?.details?.log) {
        const logContent = pollingResponse.details.log;

        if (typeof logContent === 'string' && logContent.includes('{') && logContent.includes('}')) {
          try {
            const parsedLog = JSON.parse(logContent);
            errorMessage = parsedLog.message || JSON.stringify(parsedLog);
          } catch (e) {
            errorMessage = logContent;
          }
        } else {
          errorMessage = logContent;
        }
      } else if (pollingResponse?.log) {
        // For new workflow format
        const logContent = pollingResponse.log;
        if (typeof logContent === 'string' && logContent.includes('{') && logContent.includes('}')) {
          try {
            const parsedLog = JSON.parse(logContent);
            errorMessage = parsedLog.message || JSON.stringify(parsedLog);
          } catch (e) {
            errorMessage = logContent;
          }
        } else {
          errorMessage = logContent;
        }
      } else if (pollingResponse?.details?.progress_description) {
        errorMessage = pollingResponse.details.progress_description;
      } else {
        errorMessage = 'Build or deploy process failed. No specific error details available in polling response.';
      }
    } catch (error) {

      errorMessage = 'Build or deploy process failed. Error occurred while extracting error details.';
    }

    return this.reportBuildError(projectId, statusId, errorMessage, codeFiles);
  }

  /**
   * Report a build or deploy error to the code-generation error endpoint
   * ENHANCEMENT: Sequential retry mechanism - calls /code-generation/error/build first
   * @param projectId The project ID
   * @param statusId The status ID (job ID)
   * @param errorMessage The error message to report
   * @param codeFiles Optional code files from Monaco editor (matches code-generation format)
   * @returns Observable of the error report response
   */
  reportCodeGenerationBuildError(
    projectId: string,
    statusId: string,

    errorMessage: string,
    codeFiles?: CodeFileForErrorReport[]
  ): Observable<ErrorReportResponse> {
    if (!projectId || !statusId) {

      return throwError(() => new Error('Missing required parameters: projectId and statusId are required'));
    }

    if (!errorMessage || errorMessage.trim() === '') {

      errorMessage = 'Build or deploy process failed. No specific error details available.';
    }

    const signature = this.userSignatureService.getUserSignatureSync();
    const params = new HttpParams()
      .set('user_signature',signature)
      .set('project_id', projectId)
      .set('request_id', statusId);

    // Create payload using code-generation format
    const promptObject = {
      code: codeFiles && codeFiles.length > 0 ? codeFiles.map(file => ({
        fileName: file.fileName || 'unknown.txt',
        content: file.content || ''
      })) : [],
      user_request: errorMessage.trim() // Use error message as user_request
    };

    // Create the payload according to the code-generation format
    const payload: ErrorReportRequest = {
      prompt: JSON.stringify(promptObject), // Stringify the prompt object
      image: [] // Always include empty image array for error reports
    };

    return this.http.post<ErrorReportResponse>(
      this.apiUrl + this.codeGenerationErrorEndpoint,
      payload,
      { params }
    ).pipe(
      map(response => {

        return {
          success: true,
          message: response.message || 'Code-generation error reported successfully',
          status: response.status || 'success'
        };
      }),
      catchError(error => {

        // Return error response for sequential retry mechanism
        // This will trigger fallback to regular retry
        return throwError(() => error);
      })
    );
  }

  /**
   * Validate if the provided parameters are sufficient for error reporting
   * @param projectId The project ID
   * @param statusId The status ID
   * @returns True if parameters are valid, false otherwise
   */
  validateErrorReportingParameters(projectId: string, statusId: string): boolean {
    const isValid = !!(projectId && projectId.trim() && statusId && statusId.trim());

    if (!isValid) {

    }

    return isValid;
  }
}
