import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { UserSignatureService } from './user-signature.service';
import { cacheHelpers } from '../interceptors/cache.interceptor';
import { ApiRetryService } from './api-retry.service';

export interface WireframeCodeGenerationRequest {
  // user_input: string;
  platform: string;
  framework: string;
  design_library: string;
  image?: string[]; // Empty array for wireframe generation
  docsContent?: string; // Optional text content from .txt files (separate from project_description)
  project_id?: string;
  project_name?: string;
  project_description?: string;
  project_type?: string;
  project_state?: string;
}

export interface WireframeCodeGenerationResponse {
  job_id: string;
  project_id: string;
}

@Injectable({
  providedIn: 'root'
})
export class WireframeCodeGenerationService {
  private apiUrl = environment.experienceApiUrl;
  private endpoint = '/design-generation/design-generation';
  private statusEndpoint = '/code-generation/status';

  // Map card titles to project types
  projectTypeMapper: { [key: string]: string } = {
    'Generate Wireframes': 'wireframe_generation',
    'Generate Application': 'app_generation',
  };

  constructor(
    private http: HttpClient,
    private userSignatureService: UserSignatureService,
    private apiRetryService: ApiRetryService
  ) {}

  /**
   * Generate code without an image (wireframe generation)
   * 🚨 CRITICAL: NO TIMEOUT - design-generation can take longer than 4 minutes
   * Removed all client-side timeouts to prevent 499 errors
   * @param request Request payload
   * @param userSignature User signature for tracking
   * @returns Observable with code generation response
   */
  generateWireframeCode(request: WireframeCodeGenerationRequest, userSignature?: string): Observable<WireframeCodeGenerationResponse> {
    // If userSignature is not provided, use the UserSignatureService to get it
    const signature = userSignature || this.userSignatureService.getUserSignatureSync();

    // Add user_signature to query params only, not in the payload
    const params = new HttpParams().set('user_signature', signature);

    // POST requests are not cached by default, but we're explicitly setting it for clarity
    const context = cacheHelpers.disableCache();


    // Create the HTTP request observable
    const httpRequest = this.http.post<WireframeCodeGenerationResponse>(this.apiUrl + this.endpoint, request, {
      params,
      context
      // Removed 'Connection' and 'Keep-Alive' headers as they are forbidden by browsers
      // Browser automatically manages connection keep-alive for optimal performance
    })
      .pipe(
        // 🛡️ CRITICAL: NO TIMEOUT to prevent 499 client errors
        // Let the server determine when to timeout, not the client
        // timeout() operator removed completely to prevent client-side timeouts
        map((response: any) => {
          return {
            job_id: response.job_id || '',
            project_id: response.project_id || ''
          };
        }),
        catchError(error => {

          // Enhanced 499 error handling
          if (error.status === 499) {

            // Create a more descriptive error for 499
            const enhancedError = new Error('Request was cancelled due to timeout. The operation may still be processing on the server.');
            (enhancedError as any).status = 499;
            (enhancedError as any).originalError = error;
            throw enhancedError;
          }

          // Check if it's a network/connection error
          if (error.status === 0) {
            const networkError = new Error('Network connection failed. Please check your internet connection and try again.');
            (networkError as any).status = 0;
            (networkError as any).originalError = error;
            throw networkError;
          }

          throw error;
        })
      );

    // Apply retry logic for 499 errors and network failures
    return this.apiRetryService.withRetry(httpRequest, this.apiRetryService.getWireframeRetryConfig());
  }

  /**
   * Get the status of a wireframe code generation job
   * @param jobId Job ID to check
   * @returns Observable with job status
   */
  getWireframeCodeGenerationStatus(jobId: string): Observable<any> {
    return this.http.get(`${this.apiUrl}${this.statusEndpoint}/${jobId}`)
      .pipe(
        catchError(_error => {
          return of({ status: 'error', message: 'Failed to get status' });
        })
      );
  }

  /**
   * Check the status of a code generation job
   * @param projectId Project ID
   * @param jobId Job ID to check
   * @returns Observable with job status
   */
  checkCodeGenerationStatus(projectId: string, jobId: string): Observable<any> {
    // Status endpoints should always get fresh data, so disable caching
    const context = cacheHelpers.disableCache();

    // Use the same endpoint as the regular code generation service
    // but with the wireframe-specific job ID
    return this.http.get(`${this.apiUrl}/code-generation/status/${projectId}/${jobId}`, { context })
      .pipe(
        map(response => {
          return response;
        }),
        catchError(_error => {
          return of({ status: 'error', message: 'Failed to check status' });
        })
      );
  }

  /**
   * Create a new project for wireframe generation
   * @param promptData Project data including prompt and card title
   * @param userSignature User signature for tracking
   * @returns Observable with project creation response
   */
  createWireframeProject(promptData: any, userSignature?: string): Observable<any> {
    const url = `${this.apiUrl}/wireframe-generation/create`;

    // If userSignature is not provided, use the UserSignatureService to get it
    const signature = userSignature || this.userSignatureService.getUserSignatureSync();
    const params = new HttpParams().set('user_signature', signature);

    // For prompt-to-application flow, always use 'app_generation'
    const projectType = 'app_generation';

    const payload = {
      project_name: `${promptData.selectedCardTitle || 'New'} Project`,
      project_description: promptData.prompt || 'Default description',
      project_type: projectType,
      project_state: 'ACTIVE'
    };

    // POST requests are not cached by default, but we're explicitly setting it for clarity
    const context = cacheHelpers.disableCache();

    return this.http.post<any>(url, payload, { params, context });
  }

  /**
   * Map user selections to the API request format for wireframe generation
   * @param prompt User prompt
   * @param formFactor Form factor (web, mobile, etc.)
   * @param technology Technology (angular, react, etc.)
   * @param designLibrary Design library (material, tailwind, etc.)
   * @param projectId Optional project ID
   * @param cardTitle Card title to determine project type
   * @returns Formatted request object
   */
  mapSelectionsToWireframeRequest(
    prompt: string,
    formFactor: string | null,
    technology: string | null,
    designLibrary: string | null,
    projectId?: string,
    cardTitle?: string,
    docsContent?: string // Optional text content from .txt files (separate from project_description)
  ): WireframeCodeGenerationRequest {
    // Map form factor to platform - ensure non-null string
    const platform = formFactor === 'mobile' ? 'mobile' : 'web';

    // Map technology to framework - ensure non-null string
    let framework = 'angular'; // Default
    if (technology === 'react') framework = 'react';
    if (technology === 'vue') framework = 'vue';

    // Map design library - ensure non-null string
    let designLib = 'materialui'; // Default
    if (designLibrary === 'tailwind') designLib = 'tailwind';
    if (designLibrary === 'bootstrap') designLib = 'bootstrap';

    // For prompt-to-application flow, always use 'app_generation'
    const projectType = 'app_generation';

    // Generate dynamic project name from card title
    const projectName = cardTitle
      ? `${cardTitle} Project`
      : "Generated Wireframe Project";

    // Ensure all values are non-null to match WireframeCodeGenerationRequest interface
    const request: WireframeCodeGenerationRequest = {
      // user_input: prompt || "I need to generate a wireframe application",
      platform, // Already guaranteed to be 'mobile' or 'web'
      framework, // Already guaranteed to have a default value
      design_library: designLib, // Already guaranteed to have a default value
      image: [], // Empty array for wireframe generation (no image)
      project_id: projectId,
      project_name: projectName, // Dynamic project name based on card title
      project_description: prompt || "Generated wireframe", // Use prompt as description (separate from docsContent)
      project_type: projectType, // Use the mapped project type from card title
      project_state: "ACTIVE" // Default project state
    };

    // Add docs content as separate field if provided
    if (docsContent) {
      request.docsContent = docsContent;
    }

    return request;
  }
}
