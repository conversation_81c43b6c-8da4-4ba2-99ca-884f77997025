import { Injectable, inject, signal } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, BehaviorSubject, timer, of } from 'rxjs';
import { map, catchError, timeout, retry, switchMap, takeUntil, shareReplay } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { createLogger } from '../utils/logger';
import { ToastService } from './toast.service';

/**
 * Health check response interface
 */
export interface HealthCheckResponse {
  status: 'ok' | 'error';
  message?: string;
  timestamp?: string;
}

/**
 * Health check state interface
 */
export interface HealthCheckState {
  isHealthy: boolean;
  isLoading: boolean;
  lastChecked: Date | null;
  error: string | null;
  retryCount: number;
}

/**
 * Health Check Service
 * Manages application health status and controls landing page card availability
 * Uses Angular 19+ patterns with signals and modern HTTP client
 */
@Injectable({
  providedIn: 'root'
})
export class HealthCheckService {
  private readonly http = inject(HttpClient);
  private readonly toastService = inject(ToastService);

  // Health check configuration
  private readonly healthCheckUrl = `${environment.experienceApiUrl}/health`;
  private readonly timeoutMs = 10000; // 10 seconds timeout
  private readonly maxRetries = 1; // ENHANCED: Reduced from 3 to 1 to minimize API calls
  private readonly retryDelayMs = 2000; // 2 seconds between retries

  // ENHANCED: Caching to ensure single execution per application lifecycle
  private healthCheckCache: Observable<HealthCheckResponse> | null = null;
  private healthCheckCompleted = false;
  private healthCheckInProgress = false;

  // Angular 19+ Signals for reactive state management
  private readonly healthStateSubject = new BehaviorSubject<HealthCheckState>({
    isHealthy: false,
    isLoading: false,
    lastChecked: null,
    error: null,
    retryCount: 0
  });

  // Public observables for components
  readonly healthState$ = this.healthStateSubject.asObservable();
  readonly isHealthy$ = this.healthState$.pipe(map(state => state.isHealthy));
  readonly isLoading$ = this.healthState$.pipe(map(state => state.isLoading));
  readonly error$ = this.healthState$.pipe(map(state => state.error));

  // Angular 19+ Signals for reactive access
  readonly healthState = signal<HealthCheckState>(this.healthStateSubject.value);

  constructor() {
    // Subscribe to state changes and update signal
    this.healthState$.subscribe(state => {
      this.healthState.set(state);
    });

  }

  /**
   * Perform health check - primary method called by landing page
   * ENHANCED: Implements caching to ensure single execution per application lifecycle
   * Returns observable that emits health check result
   */
  performHealthCheck(): Observable<HealthCheckResponse> {
    // ENHANCED: Return cached result if health check already completed
    if (this.healthCheckCompleted && this.healthCheckCache) {

      return this.healthCheckCache;
    }

    // ENHANCED: Return ongoing health check if already in progress
    if (this.healthCheckInProgress && this.healthCheckCache) {

      return this.healthCheckCache;
    }

    // ENHANCED: Mark health check as in progress to prevent duplicate calls
    this.healthCheckInProgress = true;

    this.updateHealthState({
      isLoading: true,
      error: null
    });

    // ENHANCED: Create cached observable for single execution
    this.healthCheckCache = this.http.get<HealthCheckResponse>(this.healthCheckUrl).pipe(
      timeout(this.timeoutMs),
      retry({
        count: this.maxRetries,
        delay: (error, retryCount) => {

          this.updateHealthState({
            retryCount: retryCount
          });
          return timer(this.retryDelayMs);
        }
      }),
      map(response => {

        const isHealthy = response.status === 'ok';
        this.updateHealthState({
          isHealthy,
          isLoading: false,
          lastChecked: new Date(),
          error: null,
          retryCount: 0
        });

        // ENHANCED: Show toast notification for unhealthy services
        if (!isHealthy) {
          this.toastService.error('Please contact admin to restart the app services');
        }

        // ENHANCED: Mark health check as completed
        this.healthCheckCompleted = true;
        this.healthCheckInProgress = false;

        return response;
      }),
      catchError((error: HttpErrorResponse) => {

        const errorMessage = this.extractErrorMessage(error);
        this.updateHealthState({
          isHealthy: false,
          isLoading: false,
          lastChecked: new Date(),
          error: errorMessage,
          retryCount: 0
        });

        // ENHANCED: Show toast notification for failed health check
        this.toastService.error('Please contact admin to restart the app services');

        // ENHANCED: Mark health check as completed even on failure
        this.healthCheckCompleted = true;
        this.healthCheckInProgress = false;

        // Return error response instead of throwing
        return of({
          status: 'error' as const,
          message: errorMessage,
          timestamp: new Date().toISOString()
        });
      }),
      // ENHANCED: Share the observable to prevent multiple HTTP calls
      shareReplay(1)
    );

    return this.healthCheckCache;
  }

  /**
   * Get current health status synchronously
   */
  getCurrentHealthStatus(): boolean {
    return this.healthState().isHealthy;
  }

  /**
   * Get current loading status synchronously
   */
  isCurrentlyLoading(): boolean {
    return this.healthState().isLoading;
  }

  /**
   * Get current error message synchronously
   */
  getCurrentError(): string | null {
    return this.healthState().error;
  }

  /**
   * Reset health check state
   */
  resetHealthState(): void {

    this.updateHealthState({
      isHealthy: false,
      isLoading: false,
      lastChecked: null,
      error: null,
      retryCount: 0
    });
  }

  /**
   * Update health state using reactive pattern
   */
  private updateHealthState(updates: Partial<HealthCheckState>): void {
    const currentState = this.healthStateSubject.value;
    const newState = { ...currentState, ...updates };
    this.healthStateSubject.next(newState);
  }

  /**
   * Extract user-friendly error message from HTTP error
   */
  private extractErrorMessage(error: HttpErrorResponse): string {
    if (error.status === 0) {
      return 'Server not Found. Please contact admin to restart the app services.';
    }

    if (error.status >= 500) {
      return 'Server is currently unavailable. Please contact admin to restart the app services.';
    }

    if (error.status === 404) {
      return 'Server not found. Please contact admin to restart the app services.';
    }

    if (error.status >= 400 && error.status < 500) {
      return 'Server request failed. Please contact admin to restart the app services.';
    }

    // Timeout or other errors
    if (error.message && error.message.includes('timeout')) {
      return 'Health check timed out. Please contact admin to restart the app services.';
    }

    return error.message || 'Server check failed. Please contact admin to restart the app services.';
  }
}
