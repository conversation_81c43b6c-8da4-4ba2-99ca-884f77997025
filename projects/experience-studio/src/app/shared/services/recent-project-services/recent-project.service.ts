import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { UserSignatureService } from '../user-signature.service';
import { cacheHelpers } from '../../interceptors/cache.interceptor';

interface ProjectResponse {
  status_code: number;
  projects: Project[];
}

export interface Project {
  project_id: string;
  project_name: string;
  project_description: string;
  project_type: string | null;
  last_modified: string;
}

// Interface for individual project detail response - Updated to match actual API response
export interface ProjectDetailResponse {
  project_details: {
    project_id: string;
    project_name: string;
    project_description: string;
    created_at: string;
    last_modified: string;
    created_by: string;
    project_state: string;
  };
  project_settings: {
    project_id: string;
    device: string;
    framework: string;
    design_system: string;
    generation_type: string;
  };
  repository_details: {
    project_id: string;
    vcs_provider: string;
    clone_url: string;
    deployment_provider: string;
    deployed_url: string;
  };
  conversation: any[];
  metadata: string;
}

// Extended project interface for detailed project information - Updated to match project_details structure
export interface ProjectDetail {
  project_id: string;
  project_name: string;
  project_description: string;
  created_at: string;
  last_modified: string;
  created_by: string;
  project_state: string;
  // Additional computed fields for backward compatibility
  project_type?: string | null;
}

@Injectable({
  providedIn: 'root'
})
export class RecentProjectService {
  private apiUrl = environment.experienceApiUrl;

  constructor(
    private http: HttpClient,
    private userSignatureService: UserSignatureService
  ) { }

  getUserProjects(userSignature?: string, numProjects: number = 10): Observable<ProjectResponse> {
    // Always use the UserSignatureService to get the user signature
    const signature = this.userSignatureService.getUserSignatureSync();

    const params = {
      user_signature: signature,
      num_projects: numProjects.toString()
    };

    // Set a custom cache max age of 2 minutes for recent projects
    // This ensures we don't show stale data for too long, but still benefit from caching
    const context = cacheHelpers.setMaxAge(2 * 60 * 1000); // 2 minutes

    return this.http.get<ProjectResponse>(`${this.apiUrl}/project`, {
      params,
      context // Pass the cache context to control caching behavior
    });
  }

  /**
   * Get user projects with cache disabled - use this when you need fresh data
   */
  getUserProjectsFresh(userSignature?: string, numProjects: number = 10): Observable<ProjectResponse> {
    // If userSignature is not provided, use the UserSignatureService to get it
    const signature = userSignature || this.userSignatureService.getUserSignatureSync();

    const params = {
      user_signature: signature,
      num_projects: numProjects.toString()
    };

    // Disable caching for this request
    const context = cacheHelpers.disableCache();

    return this.http.get<ProjectResponse>(`${this.apiUrl}/project`, {
      params,
      context // Pass the context to disable caching
    });
  }

  /**
   * Get individual project details by project ID
   * @param projectId The project ID to fetch details for
   * @param userSignature Optional user signature, will use UserSignatureService if not provided
   * @returns Observable with project detail response
   */
  getProjectById(projectId: string, userSignature?: string): Observable<ProjectDetailResponse> {
    // If userSignature is not provided, use the UserSignatureService to get it
    const signature = userSignature || this.userSignatureService.getUserSignatureSync();

    const params = {
      user_signature: signature
    };

    // Set a custom cache max age of 5 minutes for project details
    // Individual project details can be cached longer than the project list
    const context = cacheHelpers.setMaxAge(5 * 60 * 1000); // 5 minutes

    return this.http.get<ProjectDetailResponse>(`${this.apiUrl}/project/${projectId}`, {
      params,
      context // Pass the cache context to control caching behavior
    });
  }

  /**
   * Get individual project details by project ID with cache disabled
   * Use this when you need fresh project data
   * @param projectId The project ID to fetch details for
   * @param userSignature Optional user signature, will use UserSignatureService if not provided
   * @returns Observable with project detail response
   */
  getProjectByIdFresh(projectId: string, userSignature?: string): Observable<ProjectDetailResponse> {
    // If userSignature is not provided, use the UserSignatureService to get it
    const signature = userSignature || this.userSignatureService.getUserSignatureSync();

    const params = {
      user_signature: signature
    };

    // Disable caching for this request
    const context = cacheHelpers.disableCache();

    return this.http.get<ProjectDetailResponse>(`${this.apiUrl}/project/${projectId}`, {
      params,
      context // Pass the context to disable caching
    });
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return `${date.getDate()} ${date.toLocaleString('default', { month: 'short' })}`;
  }
}
