.custom-tabs-wrapper {
  display: flex;
  justify-content: space-between; // Distribute tabs evenly across container
  width: 100%; // Ensure full width
  gap: 0; // Remove fixed gap since we're using space-between
  
  .tab-item {
    display: flex;
    flex-direction: column;
    flex: 1; // Make each tab take equal space
    align-items: center; // Center content within each tab
    border: none;
    background-color: var(--custom-tabs-bg);
    cursor: pointer;
    padding: 8px 4px; // Add consistent padding
    min-width: 0; // Allow shrinking if needed

    &.active {
      .tab-icon-box {
        background: var(--custom-tabs-active-bg);
        border: 1px solid var(--custom-tabs-active-border);

        lucide-icon {
          color: var(--custom-tabs-active-icon) !important;
          svg {
            stroke : var(--custom-tabs-active-icon) !important;
          }
        }

        // Blue filter for prompt asset images when active
        img.tab-png-icon {
          filter: var(--custom-tabs-active-icon-filter);
        }
      }

      // Keep label black even when active
      .tab-label {
        color: var(--custom-tabs-label-active-text);
        font-weight: var(--custom-tabs-label-active-font-weight);
      }
    }

    &.disabled {
      opacity: var(--custom-tabs-disabled-opacity);
      cursor: not-allowed;
    }

    .tab-icon-box {
      display: flex;
      align-items: center;
      justify-content: center;
      width: clamp(48px, 15vw, 80px);
      height: clamp(24px, 5vh, 28px);
      border: 1px solid var(--custom-tabs-icon-box-border);
      border-radius: var(--custom-tabs-icon-box-border-radius);
      margin-bottom: clamp(2px, 0.5vh, 4px);
      background-color: var(--custom-tabs-icon-box-bg);
    }

    .tab-label {
      font-size: clamp(8px, 1.8vw, 10px); // Responsive font size
      font-weight: var(--custom-tabs-label-font-weight);
      color: var(--custom-tabs-label-text);
      text-align: center;
      line-height: 1.2;
      white-space: nowrap; // Prevent text wrapping
      overflow: hidden;
      text-overflow: ellipsis; // Add ellipsis for very long text
      width: 100%;
      max-width: 100%; // Ensure text doesn't overflow
    }
  }
}

// Enhanced responsive design for various zoom levels and screen sizes
@media (max-width: 1200px) {
  .custom-tabs-wrapper {
    justify-content: space-between; // Maintain equidistant layout
    gap: 0;

    .tab-item {
      flex: 1; // Maintain equal distribution
      padding: 6px 3px; // Slightly reduce padding
      
      .tab-icon-box {
        width: clamp(55px, 12vw, 80px);
        height: clamp(22px, 4vh, 26px);
      }

      .tab-label {
        font-size: clamp(7px, 1.6vw, 9px);
      }
    }
  }
}

@media (max-width: 768px) {
  .custom-tabs-wrapper {
    justify-content: space-between; // Maintain equidistant layout
    gap: 0;

    .tab-item {
      flex: 1; // Maintain equal distribution
      padding: clamp(2px, 0.3vw, 4px);
      min-height: clamp(36px, 6vh, 44px);

      .tab-icon-box {
        width: clamp(45px, 10vw, 70px);
        height: clamp(20px, 3.5vh, 24px);
        margin-bottom: clamp(1px, 0.3vh, 3px);
      }

      .tab-label {
        font-size: clamp(6px, 1.4vw, 8px);
        color: var(--custom-tabs-label-text);
      }
    }
  }
}

@media (max-width: 480px) {
  .custom-tabs-wrapper {
    justify-content: space-between; // Maintain equidistant layout
    gap: 0;

    .tab-item {
      flex: 1; // Maintain equal distribution
      padding: clamp(1px, 0.2vw, 3px);
      min-height: clamp(32px, 5vh, 40px);

      .tab-icon-box {
        width: clamp(35px, 8vw, 60px);
        height: clamp(18px, 3vh, 22px);
        margin-bottom: clamp(1px, 0.2vh, 2px);
      }

      .tab-label {
        font-size: clamp(5px, 1.2vw, 7px);
        color: var(--custom-tabs-label-text);
      }
    }
  }
}

// Extra small screens and high zoom levels
@media (max-width: 320px) {
  .custom-tabs-wrapper {
    justify-content: space-between; // Maintain equidistant layout
    gap: 0;

    .tab-item {
      flex: 1; // Maintain equal distribution
      padding: 1px;
      min-height: 30px;

      .tab-icon-box {
        width: clamp(30px, 6vw, 50px);
        height: clamp(16px, 2.5vh, 20px);
        margin-bottom: 1px;
      }

      .tab-label {
        font-size: clamp(4px, 1vw, 6px);
        color: var(--custom-tabs-label-text);
      }
    }
  }
}

// Container queries for better adaptation (if supported)
@container (max-width: 400px) {
  .custom-tabs-wrapper {
    justify-content: space-between; // Maintain equidistant layout
    
    .tab-item {
      flex: 1; // Maintain equal distribution
      
      .tab-icon-box {
        width: clamp(40px, 8cqw, 65px);
        height: clamp(18px, 3cqh, 24px);
      }

      .tab-label {
        font-size: clamp(6px, 1.5cqw, 8px);
      }
    }
  }
}

// High zoom level adjustments (when viewport becomes very narrow due to zoom)
@media (max-width: 250px) {
  .custom-tabs-wrapper {
    justify-content: space-between; // Maintain equidistant layout
    
    .tab-item {
      flex: 1; // Maintain equal distribution
      
      .tab-icon-box {
        width: 25px;
        height: 14px;
      }

      .tab-label {
        font-size: 4px;
        line-height: 1;
      }
    }
  }
}
