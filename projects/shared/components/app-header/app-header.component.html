<!-- Hide header on login pages -->
<div *ngIf="!isLoginPage">
  <!-- SVG Clip Path Definition for Header Design -->
  <svg width="0" height="0" style="position: absolute">
    <defs>
      <clipPath id="headerClip" clipPathUnits="objectBoundingBox">
        <path
          d="
          M 0.03,0 
          L 0.97,0 
          L 0.95,0.71 
          Q 0.939,1    0.91,1 
          L 0.09,1 
          Q 0.061,1    0.05,0.69 
          Z"
        />
      </clipPath>
    </defs>
  </svg>

  <awe-header theme="light">
    <div left-content>
      <div
        class="animated-logo-container"
        [attr.data-studio]="currentStudioName"
        [attr.data-index]="currentLogoIndex"
        (mouseenter)="pauseLogoAnimation()"
        (mouseleave)="resumeLogoAnimation()"
      >
        <img
          [src]="currentLogo"
          class="header-logo animated-logo"
          [class.logo-transitioning]="isLogoAnimating"
          [alt]="currentStudioName + ' Logo'"
        />

        <!-- Optional: Studio indicator dots -->
        <div class="studio-indicators" *ngIf="studioLogos.length > 1">
          <div
            *ngFor="let logo of studioLogos; let i = index"
            class="studio-dot"
            [class.active]="i === currentLogoIndex"
          ></div>
        </div>
      </div>
    </div>

    <div center-content>
      <div class="header-wrapper">
        <div class="header-shadow"></div>
        <div class="nav-menu">
          <div class="nav-items" [class]="navItemsClasses">
            <!-- Navigation items -->
            <shared-nav-item
              *ngFor="let item of config.navItems; let i = index"
              [label]="item.label"
              [route]="item.route"
              [selected]="item.selected"
              [hasDropdown]="item.hasDropdown"
              [dropdownOpen]="item.dropdownOpen || false"
              [dropdownItems]="item.dropdownItems || []"
              [icon]="item.icon"
              [disabled]="item.disabled || false"
              (toggleDropdownEvent)="toggleDropdown(i)"
              (navigateEvent)="navigateTo($event)"
              (selectEvent)="selectMenuItem(i)"
              (dropdownItemSelected)="onDropdownItemSelected($event, i)"
              (dropdownPortalOpen)="onDropdownPortalOpen($event)"
              class="nav-item-wrapper"
            >
            </shared-nav-item>
          </div>
        </div>
      </div>
    </div>

    <div right-content class="user-info-container">
      <!-- Organization Selector (if enabled) -->
      <div *ngIf="config.showOrgSelector" class="org-path-dropdown-container">
        <div
          class="org-path-trigger"
          (click)="toggleOrgDialog()"
          #orgPathTrigger
        >
          <span class="org-icon">
            <img
              src="assets/svgs/ascendion-logo/header-ascendion-logo.svg"
              alt="Organization Logo"
              width="40"
              height="40"
            />
          </span>
          <span class="org-label-text">
            <span>{{ orgLabel }}</span>
          </span>
          <span class="org-dropdown-arrow" [class.open]="isOrgDialogOpen">
            <svg width="16" height="16" viewBox="0 0 12 12" fill="none">
              <path
                d="M2.5 4L6 7.5L9.5 4"
                stroke="currentColor"
                stroke-width="1.5"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </span>
        </div>

        <!-- Organization Configuration Dialog -->
        <div
          *ngIf="isOrgDialogOpen"
          class="org-path-popover"
          #popover
          [ngClass]="popoverAlign"
        >
          <form [formGroup]="headerConfigForm">
            <div class="filter-config-title">Filter Configuration</div>
            <div class="dropdown-row-vertical">
              <label class="filter-label required">Choose Organization</label>
              <ava-dropdown
                [dropdownTitle]="'Select Organization'"
                [options]="orgOptions"
                [selectedValue]="selectedOrgName"
                [disabled]="false"
                (selectionChange)="onOrgSelect($event)"
                [search]="true"
                [enableSearch]="true"
              >
              </ava-dropdown>

              <label class="filter-label required">Choose Domain</label>
              <ava-dropdown
                [dropdownTitle]="'Select Domain'"
                [options]="domainOptions"
                [selectedValue]="selectedDomainName"
                [disabled]="!selectedOrg"
                (selectionChange)="onDomainSelect($event)"
                [search]="true"
                [enableSearch]="true"
              >
              </ava-dropdown>

              <label class="filter-label required">Choose Project</label>
              <ava-dropdown
                [dropdownTitle]="'Select Project'"
                [options]="projectOptions"
                [selectedValue]="selectedProjectName"
                [disabled]="!selectedDomain"
                (selectionChange)="onProjectSelect($event)"
                [search]="true"
                [enableSearch]="true"
              >
              </ava-dropdown>

              <label class="filter-label required">Choose Team</label>
              <ava-dropdown
                [dropdownTitle]="'Select Team'"
                [options]="teamOptions"
                [selectedValue]="selectedTeamName"
                [disabled]="!selectedProject"
                (selectionChange)="onTeamSelect($event)"
                [search]="true"
                [enableSearch]="true"
              >
              </ava-dropdown>
            </div>
            <div class="popover-actions">
              <ava-button
                label="Cancel"
                variant="secondary"
                size="medium"
                (userClick)="closeOrgDialog()"
              >
              </ava-button>
              <ava-button
                label="Apply"
                variant="primary"
                size="medium"
                [disabled]="!headerConfigForm.valid"
                (userClick)="saveOrgPathAndClose()"
              >
              </ava-button>
            </div>
          </form>
        </div>

        <!-- Organization dialog backdrop -->
        <div
          *ngIf="isOrgDialogOpen"
          class="org-path-backdrop"
          (click)="closeOrgDialog()"
        ></div>
      </div>

      <!-- App Drawer (if enabled) -->
      <div *ngIf="config.showAppDrawer" class="app-drawer-container">
        <div
          class="app-drawer-trigger"
          [class.active]="isAppDrawerOpen"
          (click)="toggleAppDrawer()"
        >
          <ava-icon iconName="layout-grid"></ava-icon>
        </div>

        <!-- App Drawer Backdrop -->
        <div
          *ngIf="isAppDrawerOpen"
          class="app-drawer-backdrop"
          (click)="closeAppDrawer()"
        ></div>

        <!-- App Drawer Dropdown -->
        <div class="app-drawer-dropdown" [class.visible]="isAppDrawerOpen">
          <div class="app-drawer-content">
            <!-- <div class="app-drawer-header">
            <h3>Ascendion Studios</h3>
          </div> -->
            <div class="app-drawer-grid">
              <div
                *ngFor="let app of getFilteredApps()"
                class="app-drawer-item"
                (click)="navigateToApp(app)"
              >
                <div class="app-icon">
                  <img [src]="app.icon" [alt]="app.name" />
                </div>
                <div class="app-info">
                  <div class="app-name">{{ app.name }}</div>
                  <div class="app-description" *ngIf="app.description">
                    {{ app.description }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Theme Toggle (if enabled and app drawer is disabled) -->
      <div
        *ngIf="config.showThemeToggle && !config.showAppDrawer"
        class="theme-toggle"
        (click)="toggleTheme()"
      >
        <img [src]="themeMenuIcon" alt="Toggle Theme" width="24" height="24" />
      </div>

      <!-- Profile Dropdown -->
      <div *ngIf="config.showProfileDropdown" class="profile-container">
        <div
          class="profile-trigger"
          [class.active]="profileDropdownOpen"
          (click)="toggleProfileDropdown()"
        >
          <img [src]="userAvatar" alt="User Profile" class="profile-avatar" />
        </div>

        <!-- Enhanced Profile Dropdown -->
        <div class="profile-dropdown" [class.visible]="profileDropdownOpen">
          <div class="profile-dropdown-content">
            <!-- User Info Section -->
            <div class="profile-user-info">
              <div class="profile-avatar-large">
                <img [src]="userAvatar" alt="User Profile" />
              </div>
              <div class="profile-details">
                <div class="profile-name">{{ userName }}</div>
                <div class="profile-designation" *ngIf="userDesignation">
                  {{ userDesignation }}
                </div>
                <div class="profile-email" *ngIf="userEmail">
                  {{ userEmail }}
                </div>
              </div>
            </div>

            <div class="profile-divider"></div>

            <!-- Theme Toggle Section (if enabled) -->
            <div
              *ngIf="config.showThemeToggleInProfile"
              class="profile-section"
            >
              <div class="profile-section-header">
                <span class="section-title">Mode</span>
              </div>
              <div class="theme-toggle-container">
                <button
                  class="theme-option"
                  [class.active]="currentTheme === 'light'"
                  (click)="currentTheme !== 'light' && toggleTheme()"
                >
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <circle cx="12" cy="12" r="5" />
                    <path
                      d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42"
                    />
                  </svg>
                  Light
                </button>
                <button
                  class="theme-option"
                  [class.active]="currentTheme === 'dark'"
                  (click)="currentTheme !== 'dark' && toggleTheme()"
                >
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                  >
                    <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z" />
                  </svg>
                  Dark
                </button>
              </div>
            </div>

            <!-- Language Switcher Section (if enabled) -->
            <div *ngIf="config.showLanguageSwitcher" class="profile-section">
              <div class="profile-section-header">
                <span class="section-title">Language</span>
              </div>
              <div class="language-options">
                <button
                  *ngFor="let language of config.availableLanguages"
                  class="language-option"
                  [class.active]="currentLanguage === language.code"
                  (click)="switchLanguage(language.code)"
                >
                  {{ language.name }}
                </button>
              </div>
            </div>

            <div class="profile-divider"></div>

            <!-- Actions Section -->
            <div class="profile-actions">
              <button class="profile-action-item logout-btn" (click)="logout()">
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4" />
                  <polyline points="16,17 21,12 16,7" />
                  <line x1="21" y1="12" x2="9" y2="12" />
                </svg>
                Sign Out
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </awe-header>
</div>

<!-- Dropdown Portal Container -->
<div
  *ngIf="dropdownPortal.open && dropdownPortal.rect"
  class="dropdown-portal-menu"
  [style.top]="dropdownPortal.rect.bottom + 4 + 'px'"
  [style.left]="dropdownPortal.rect.left + 'px'"
>
  <div class="dropdown-menu">
    <div
      *ngFor="let item of dropdownPortal.items"
      class="dropdown-item"
      (click)="
        onDropdownItemSelected({ route: item.route, label: item.label }, 0)
      "
    >
      <img *ngIf="item.icon" [src]="item.icon" alt="" class="dropdown-icon" />
      <div class="dropdown-content">
        <div class="dropdown-label">{{ item.label }}</div>
        <div class="dropdown-description">{{ item.description }}</div>
      </div>
    </div>
  </div>
</div>
