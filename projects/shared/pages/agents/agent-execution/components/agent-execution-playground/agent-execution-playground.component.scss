.playground-container {
  display: flex;
  flex-direction: column;
  font-family: "Inter", sans-serif;
  width: 100%;
  height: 78vh; /* Auto height instead of fixed */
  max-height: 100%; /* Use available height */
  overflow: hidden; /* Prevent container overflow */
  @media (max-width: 1400px) {
    width: 60%;
  }

  @media (max-width: 1200px) {
    width: 60%;
  }
}

/* Top Button Container */
.button-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  height: 48px;
  flex-shrink: 0;
  border-bottom: 1px solid #eee;
  background-color: #ffffff;
}

/* Dropdown Button */
.dropdown-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 6px;
  background: white;
  color: #333;
  font-size: 14px;
  cursor: pointer;
  min-width: 100px;
}

.dropdown-btn .arrow-down {
  width: 10px;
  height: 6px;
  margin-left: 8px;
}

/* Action Button and Menu */
.btn-menu {
  display: flex;
  align-items: center;
  gap: 8px;
}

//.action-btn {
//padding: 0.25rem 0.5rem;
//background: lightblue; //replace this
//color: white;
//border: none;
//border-radius: 6px;
//cursor: pointer;
//font-size: 14px;
//}

/* Three Dot Menu */
.menu-icon {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 3px;
  cursor: pointer;
}

.menu-icon span {
  display: block;
  width: 3px;
  height: 3px;
  background: black;
  border-radius: 50%;
}

/* Layout (chat area) */
.layout {
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex: 1; /* fill available space */
  padding: 16px;
  overflow-y: auto; /* Enable scrolling within the chat area */
  overflow-x: hidden; /* Hide horizontal scroll */
  background: #fff;
  align-items: stretch;
  justify-content: flex-start;
  min-height: 300px; /* Minimum height for better appearance */
  max-height: none; /* Remove height restriction */
}

/* Chat messages common style */
.message {
  max-width: 60%;
  font-size: 14px;
  border-radius: 8px;
  padding: 16px 24px;
}

.message-row {
  display: flex;
  width: 100%;
}

.message-row.ai {
  justify-content: flex-start;
}

.message-row.user {
  justify-content: flex-end;
}

/* User messages (left side, same as bot) */

.user-message {
  display: inline-flex; /* bubble fits to content */
  flex-direction: column;
  justify-content: center;
  align-items: flex-start; /* text left-aligned inside */
  align-self: flex-end; /* positions entire bubble right side */
  background: #c2c4cd;
  color: #333;
  border-radius: 8px;
  padding: 16px 24px;
  max-width: 60%;
  word-wrap: break-word;
  text-align: left;
  margin-bottom: 0.5rem;
}

.user-message:empty {
  background: none;
  padding: 0;
}

/* Bot messages (left side) */
.bot-response {
  align-self: flex-start;
  background: #f1f1f1; /* Neutrals-N-100 */
  color: #333;
  display: flex;
  padding: 16px 24px;
  border-radius: 8px;
  flex-direction: column;
  justify-content: center;
  gap: 10px;
  margin-bottom: 0.5rem;
  position: relative;
  padding-right: 40px;
}

/* Status message styling */
.status-message {
  align-self: flex-start;
  background: #f8fafc;
  color: #374151;
  display: flex;
  padding: 12px 16px;
  border-radius: 6px;
  flex-direction: column;
  justify-content: center;
  gap: 8px;
  margin-bottom: 0.5rem;
  position: relative;
  border: 1px solid #e5e7eb;
  font-size: 14px;
  font-weight: 500;
  
  &.success {
    background: rgba(16, 185, 129, 0.1);
    border-color: rgba(16, 185, 129, 0.2);
    color: #10b981;
  }
  
  &.failed {
    background: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.2);
    color: #ef4444;
  }
}

.copy-btn {
  position: absolute;
  top: 16px;
  right: 16px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;

  svg {
    fill: var(--color-brand-primary, #144692); // or your brand color
    transition: fill 0.2s ease;
  }

  &:hover svg {
    fill: #1d4ed8; // darker blue on hover
  }
}

/* Loading message styling */
.loading-message {
  min-height: 20px;
  display: flex;
  align-items: center;
}

/* Typing indicator with animated dots */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
}

.typing-indicator .dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #666;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator .dot:nth-child(1) {
  animation-delay: 0s;
}

.typing-indicator .dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator .dot:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%,
  60%,
  100% {
    transform: scale(1);
    opacity: 0.5;
  }
  30% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* Result Label (optional) */
.result-label {
  display: flex;
  width: 186px;
  padding: 8px 12px;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  background: #f1f1f1;
  font-size: 14px;
  color: #333;
  margin-bottom: 0.5rem;
}

/* Input container at bottom */

/* Toggle container */
.toggle-container {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  padding: 8px 24px;
  font-family: "Inter", sans-serif;
  flex-shrink: 0; /* Prevent toggle from shrinking */
  height: 40px; /* Fixed height */
  min-height: 40px; /* Fixed minimum height */
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 20px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 14px;
  width: 14px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

.toggle-switch input:checked + .slider {
  background-color: #4caf50;
}

.toggle-switch input:checked + .slider:before {
  transform: translateX(20px);
}

.toggle-label {
  color: #333;
}

.dot-dropdown-menu {
  position: absolute;
  right: 12px;
  top: 120px;
  background: white;
  border: 1px solid #ccc;
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  z-index: 10;
  display: flex;
  flex-direction: column;
  min-width: 120px;
}

.dot-dropdown-menu button:hover {
  background: #f0f0f0;
}

.dot-dropdown-menu button {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 8px;
  padding: 10px 16px;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  font-size: 14px;
  color: #333;
  width: 100%;
  line-height: 1.2;
}

.dot-dropdown-menu button svg {
  flex-shrink: 0;
  display: block;
  height: 16px;
  width: 16px;
  vertical-align: middle;
}

//list drop down
.dropdown-container {
  position: relative;
  display: inline-block;
  width: 30%;
}

/* Agent Name Display */
.agent-name-display {
  position: relative;
  display: inline-block;
  width: 30%;

  .disabled-agent-name-input {
    ::ng-deep .ava-textbox {
      input {
        background-color: #f8f9fa !important;
        color: #6c757d !important;
        cursor: not-allowed !important;
        border-color: #e9ecef !important;
      }
    }
  }
}

.tool-dropdown-menu {
  position: absolute;
  top: 100%; /* ✅ below the button */
  left: 0;
  display: flex;
  flex-direction: column;
  background: white;
  border: 1px solid #ccc;
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  z-index: 10;
  min-width: 140px;
}

/* Toggle Container - positioned below input */
.toggle-container {
  display: flex;
  flex-direction: row;
  gap: 16px;
  align-items: center;
  padding: 12px 16px;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
  min-height: 56px;
  flex-shrink: 0; /* Prevent shrinking */
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.toggle-row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  min-height: 32px;
  white-space: nowrap;
}

/* File Upload Styles */
.uploaded-files {
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e0e0e0;
  border-bottom: none;
  border-radius: 8px 8px 0 0;
  padding: 12px;
  max-height: 120px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

.file-item {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  min-height: 48px;
  transition: all 0.2s ease;
  position: relative;

  &:hover {
    background: #e9ecef;
    border-color: #ced4da;
  }
}

.file-name {
  flex: 1;
  font-weight: 500;
  color: #495057;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 12px;

  &::before {
    content: "📄";
    margin-right: 8px;
    font-size: 16px;
  }
}

.remove-file {
  background: #dc3545;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 12px;
  line-height: 1;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
  transition: background-color 0.2s ease;

  &:hover {
    background: #c82333;
  }

  &::after {
    content: "Remove";
  }
}

/* Chat message file attachment styling */
:host ::ng-deep .message-content {
  .file-attachment-info {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 8px 12px;
    margin-top: 8px;
    font-size: 12px;
    color: #6c757d;

    &::before {
      content: "📎";
      margin-right: 6px;
    }
  }
}

.copied-toast {
  position: fixed;
  bottom: 24px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #333;
  color: #fff;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25);
  animation: fadeInOut 2s ease-in-out;
}

@keyframes fadeInOut {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(10px);
  }
  10%,
  90% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateX(-50%) translateY(-10px);
  }
}

.generating-indicator {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  padding: 5px 20px;
  background: var(--color-background-primary);
  // border-top: 1px solid var(--color-border-primary);
  // border-bottom: 1px solid var(--color-border-primary);

  .generating-stepper {
    display: flex;
    align-items: center;
    gap: 8px;

    .modern-loading-spinner {
      width: 20px;
      height: 20px;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      .spinner-ring {
        position: absolute;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        border: 2.5px solid transparent;
        border-top-color: #0084ff;
        border-bottom-color: #03bdd4;
        filter: drop-shadow(0 0 1px rgba(101, 102, 205, 0.3));
        animation: spin-ring 1.5s ease-in-out infinite;
      }

      .spinner-core {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background: linear-gradient(135deg, #0084ff 0%, #03bdd4 100%);
        box-shadow: 0 0 8px rgba(176, 217, 255, 0.5);
        animation: pulse 1.5s ease-in-out infinite alternate;
      }
    }

    .generating-text {
      font-size: 12px;
      color: black;
      font-weight: 500;
      position: relative;
      overflow: hidden;
      // background: linear-gradient(
      //   90deg,
      //   var(--color-text-secondary, #666) 25%,
      //   var(--code-viewer-bg) 50%,
      //   var(--color-text-secondary, #666) 75%
      // );
      // background-size: 200% 100%;
      // background-clip: text;
      // -webkit-background-clip: text;
      // -webkit-text-fill-color: transparent;
      animation: minimal-text-shine 1.5s ease-in-out infinite;
    }
  }

  // Dark theme support
  &.dark {
    .generating-stepper {
      .generating-text {
        color: var(--color-text-secondary-dark, #ccc);

        background: linear-gradient(
          90deg,
          var(--color-text-secondary-dark, #ccc) 25%,
          var(--code-viewer-bg) 50%,
          var(--color-text-secondary-dark, #ccc) 75%
        );
        background-size: 200% 100%;
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }
  }
}

// Animations
@keyframes spin-ring {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes minimal-text-shine {
  0% {
    background-position: 100% 0;
  }
  100% {
    background-position: -100% 0;
  }
}

.input-container {
  position: relative;
  background: #fff;
  border: 2px solid #03acc1;
  border-radius: 16px;
  padding: 12px;
  margin: 1rem;
  box-sizing: border-box;
  min-height: 80px;
}

/* Textarea fills top part only */
.input-container textarea {
  width: 100%;
  border: none;
  resize: none;
  background: transparent;
  font-size: 14px;
  font-family: "Inter", sans-serif;
  line-height: 1.4;
  outline: none;
  padding: 0;
  padding-right: 48px; /* space for send button */
  box-sizing: border-box;
  min-height: calc(3em * 1); /* start small */
  max-height: calc(1.4em * 3); /* ≈3 lines */
  overflow-y: auto;
}

.input-container textarea::placeholder {
  color: #999;
}

/* Attach icon bottom-left */
.attach-btn {
  position: absolute;
  bottom: 8px;
  left: 12px;
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
}

.attach-btn svg {
  width: 18px;
  height: 18px;
  fill: #e91e63;
}

/* Edit and Send bottom-right */
.right-icons {
  position: absolute;
  bottom: 8px;
  right: 12px;
  display: flex;
  gap: 8px;
}

.right-icons button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
}

.right-icons svg {
  width: 18px;
  height: 18px;
  fill: #e91e63;
}

/* Agent Details Container */
.agent-details-container {
  flex: 1;
  margin-right: 16px;
  padding: 12px 16px;
  background: var(--color-surface-secondary, #f8f9fa);
  border: 1px solid var(--color-border-primary, #e1e5e9);
  border-radius: 8px;

  .agent-info {
    .agent-name {
      font-size: 16px;
      font-weight: 600;
      color: var(--color-text-primary, #1a1d21);
      margin: 0 0 4px 0;
    }

    .agent-description {
      font-size: 14px;
      color: var(--color-text-secondary, #6c757d);
      margin: 0 0 8px 0;
      line-height: 1.4;
    }

    .agent-meta {
      display: flex;
      flex-direction: column;
      gap: 4px;

      .agent-role,
      .agent-goal {
        font-size: 12px;
        color: var(--color-text-tertiary, #8e9297);
        font-weight: 500;
      }
    }
  }
}
