::ng-deep .ava-dropdown {
  width: 100% !important;
}

.mt-5 {
  margin-top: 2rem;
}

.filter-buttons-section {
  padding-top: 10px;
}

::ng-deep .ava-text-card-container .create-type .ava-card-container .ava-card.card {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 326px;
  border-radius: 16px;
  background: var(--card-background-gradient) !important;
  box-shadow: var(--card-box-shadow) !important;
  transition: transform .4s ease-out, box-shadow .4s ease-out;
  overflow: hidden;
}
