<div class="preview-panel">
  <div class="backdrop" (click)="closePreview()"></div>
<app-preview-panel class="panel-container" [divider]="false" (click)="$event.stopPropagation()">
<div panel-header class="preview-header">
  <span class="panel-title">Metadata Information</span>
  <ava-icon iconName="x" iconColor="black" class="close-btn" (click)="closePreview()"></ava-icon>
</div>
<div panel-content class="preview-content">
  <!-- Loading State -->
  <div *ngIf="previewData?.loading" class="preview-loading">
    <div class="loading-spinner"></div>
    <p>Loading details...</p>
  </div>

  <!-- Content based on preview data -->
  <div *ngIf="previewData?.data && !previewData?.loading">
    <!-- Model Preview -->
    <div *ngIf="previewData?.type === 'model'" class="model-preview">
      <div class="model-section">
        <h3>Model Details</h3>
        
        <div class="model-field" *ngIf="previewData?.title || previewData.data.name">
          <label>Model Name</label>
          <div class="field-value">{{ previewData?.title || previewData.data.name }}</div>
        </div>

        <div class="model-field" *ngIf="previewData.data.modelDescription || previewData.data.description">
          <label>Description</label>
          <div class="field-value description-text">{{ previewData.data.modelDescription || previewData.data.description }}</div>
        </div>

        <div class="model-meta">
          <div class="meta-row">
            <div class="meta-item" *ngIf="previewData.data.createdBy">
              <label>Added by</label>
              <div class="field-value">{{ previewData.data.createdBy}}</div>
            </div>
            <div class="meta-item" *ngIf="previewData.data.createdOn || previewData.data.createdDate">
              <label>Added on</label>
              <div class="field-value">{{ previewData.data.createdOn || previewData.data.createdDate | date:'MM/dd/yyyy' }}</div>
            </div>
          </div>
        </div>
      </div>

      <div class="model-section">
        <h3>Model Configuration</h3>
        
        <div class="config-field" *ngIf="previewData.data.modelType">
          <label>Select Model</label>
          <div class="dropdown-display">
            <span>{{ previewData.data.modelType}}</span>
          </div>
        </div>

        <!-- Temperature -->
        <div class="config-field" *ngIf="previewData.data.temperature !== undefined && previewData.data.temperature !== null">
          <label>Temperature</label>
          <ava-slider [min]="0" [max]="1" [step]="0.01"
              [value]="previewData.data.temperature" class="disabled-slider"></ava-slider>
        </div>

        <!-- Max Tokens and Top P in same row -->
        <div class="config-row" *ngIf="previewData.data.maxToken || previewData.data.topP">
          <div class="config-field half-width" *ngIf="previewData.data.maxToken">
            <label>Max Token</label>
            <ava-textbox 
              [value]="previewData.data.maxToken" 
              placeholder="4000"
              type="number"
              [disabled]="true">
            </ava-textbox>
            <div class="field-hint">4096 Tokens used</div>
          </div>

          <div class="config-field half-width" *ngIf="previewData.data.topP">
            <label>Top P</label>
            <ava-textbox 
              [value]="previewData.data.topP" 
              placeholder="0.95"
              type="number"
              step="0.01"
              [disabled]="true">
            </ava-textbox>
          </div>
        </div>

        <!-- Max Iteration -->
        <div class="config-field" *ngIf="previewData.data.maxIteration">
          <label>Max Iteration</label>
          <ava-textbox 
            [value]="previewData.data.maxIteration" 
            placeholder="1"
            type="number"
            [disabled]="true">
          </ava-textbox>
        </div>

        <div class="config-toggle">
          <span class="toggle-text" (click)="toggleConfigDetails()">
            View More Configuration Details {{ showMoreConfig ? '-' : '+' }}
          </span>
        </div>

        <!-- Collapsible Configuration Fields -->
        <div class="config-details" [class.expanded]="showMoreConfig">
          <div class="config-field" *ngIf="previewData.data.aiEngine">
            <label>AI Engine</label>
            <div class="dropdown-display">
              <span class="engine-icon"></span>
              <span>{{ previewData.data.aiEngine}}</span>
            </div>
          </div>

          <div class="config-field" *ngIf="previewData.data.modelType">
            <label>Model Type</label>
            <div class="dropdown-display">
              <span>{{ previewData.data.modelType}}</span>
            </div>
          </div>

          <div class="config-field" *ngIf="previewData.data.baseurl || previewData.data.baseUrl">
            <label>Base URL</label>
            <div class="input-display">{{ previewData.data.baseurl || previewData.data.baseUrl }}</div>
          </div>

          <div class="config-field" *ngIf="previewData.data.llmDeploymentName">
            <label>LLM Deployment Name</label>
            <div class="input-display">{{ previewData.data.llmDeploymentName }}</div>
          </div>

          <div class="config-field" *ngIf="previewData.data.apiKey">
            <label>API Key Encoded</label>
            <div class="input-display">{{ previewData.data.apiKey}}</div>
          </div>

          <div class="config-field" *ngIf="previewData.data.apiVersion">
            <label>API Version</label>
            <div class="input-display">{{ previewData.data.apiVersion }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Tool Preview -->
    <div *ngIf="previewData?.type === 'tool'" class="tool-preview">
      <div class="model-section">
        <h3>Tool Details</h3>
        
        <div class="model-field" *ngIf="previewData?.title || previewData.data.name">
          <label>Tool Name</label>
          <div class="field-value">{{ previewData?.title || previewData.data.name }}</div>
        </div>

        <div class="model-field" *ngIf="previewData.data.description || previewData.data.appDescription">
          <label>Description</label>
          <div class="field-value description-text">{{ previewData.data.description || previewData.data.appDescription }}</div>
        </div>

        <div class="model-field" *ngIf="previewData.data.areaOfScope">
          <label>Area of Scope</label>
          <div class="field-value description-text">{{ previewData.data.areaOfScope }}</div>
        </div>

        <div class="model-meta">
          <div class="meta-row">
            <div class="meta-item" *ngIf="previewData.data.createdBy">
              <label>Added by</label>
              <div class="field-value">{{ previewData.data.createdBy }}</div>
            </div>
            <div class="meta-item" *ngIf="previewData.data.createdOn || previewData.data.createdDate">
              <label>Added on</label>
              <div class="field-value">{{ previewData.data.createdOn || previewData.data.createdDate | date:'MM/dd/yyyy' }}</div>
            </div>
          </div>
        </div>
      </div>

      <div class="model-section">
        <h3>Tool Configuration</h3>
        
        <div class="config-field" *ngIf="previewData.data.functionality || previewData.data.content || previewData.data.toolClassDef">
          <div class="code-content">{{ previewData.data.functionality || previewData.data.content || previewData.data.toolClassDef || 'No tool definition available' }}</div>
        </div>
      </div>
    </div>

    <!-- Prompt Preview -->
    <div *ngIf="previewData?.type === 'prompt'" class="prompt-preview">
      <div class="model-section">
        <h3>Prompt Details</h3>
        
        <div class="model-field" *ngIf="previewData?.title || previewData.data.name">
          <label>Prompt Name</label>
          <div class="field-value">{{ previewData?.title || previewData.data.name }}</div>
        </div>

        <div class="model-field" *ngIf="previewData.data.description">
          <label>Description</label>
          <div class="field-value description-text">{{ previewData.data.description }}</div>
        </div>

        <div class="model-field" *ngIf="previewData.data.role">
          <label>Role</label>
          <div class="field-value">{{ previewData.data.role }}</div>
        </div>

        <div class="model-field" *ngIf="previewData.data.goal">
          <label>Goal</label>
          <div class="field-value description-text">{{ previewData.data.goal }}</div>
        </div>

        <div class="model-field" *ngIf="previewData.data.backstory">
          <label>Backstory</label>
          <div class="field-value description-text">{{ previewData.data.backstory }}</div>
        </div>

        <div class="model-meta">
          <div class="meta-row">
            <div class="meta-item" *ngIf="previewData.data.createdBy">
              <label>Added by</label>
              <div class="field-value">{{ previewData.data.createdBy }}</div>
            </div>
            <div class="meta-item" *ngIf="previewData.data.updatedAt || previewData.data.createdAt">
              <label>Added on</label>
              <div class="field-value">{{ previewData.data.updatedAt || previewData.data.createdAt | date:'MM/dd/yyyy' }}</div>
            </div>
          </div>
        </div>
      </div>

      <div class="model-section">
        <h3>Prompt Configuration</h3>
        
        <div class="config-field" *ngIf="previewData.data.promptTask || previewData.data.template || previewData.data.content">
          <label>Freeform Prompt <span class="required">*</span></label>
          <div class="prompt-content">{{ previewData.data.promptTask || previewData.data.template || previewData.data.content }}</div>
        </div>

        <div class="config-field" *ngIf="previewData.data.expectedOutput">
          <label>Expected Output</label>
          <div class="prompt-content">{{ previewData.data.expectedOutput }}</div>
        </div>
      </div>
    </div>

    <!-- Knowledge Base Preview -->
    <div *ngIf="previewData?.type === 'knowledge'" class="knowledge-preview">
      <div class="model-section">
        <h3>Knowledge Base Details</h3>
        
        <div class="model-field" *ngIf="previewData.data.name || previewData?.title">
          <label>Knowledge Base Name</label>
          <div class="field-value">{{ previewData.data.name || previewData?.title }}</div>
        </div>

        <div class="model-field" *ngIf="previewData.data.description">
          <label>Description</label>
          <div class="field-value description-text">{{ previewData.data.description }}</div>
        </div>

        <div class="model-meta">
          <div class="meta-row">
            <div class="meta-item" *ngIf="previewData.data.createdBy">
              <label>Added by</label>
              <div class="field-value">{{ previewData.data.createdBy}}</div>
            </div>
            <div class="meta-item" *ngIf="previewData.data.createdDate || previewData.data.createdOn">
              <label>Added on</label>
              <div class="field-value">{{ previewData.data.createdDate || previewData.data.createdOn | date:'MM/dd/yyyy' }}</div>
            </div>
          </div>
        </div>
      </div>

      <div class="model-section">
        <h3>Knowledge Base Configuration</h3>
        
        <div class="config-field" *ngIf="previewData.data.embeddingModel">
          <label>Embedding Model</label>
          <div class="dropdown-display">
            <span>{{ previewData.data.embeddingModel }}</span>
          </div>
        </div>

        <div class="config-field" *ngIf="previewData.data.splitSize !== undefined && previewData.data.splitSize !== null">
          <label>Split Size</label>
          <ava-slider [min]="0" [max]="1" [step]="0.01"
              [value]="previewData.data.splitSize" class="disabled-slider"></ava-slider>
        </div>

        <div class="config-field" *ngIf="previewData.data.uploadType">
          <label>Upload Type</label>
          <div class="input-display">{{ previewData.data.uploadType}}</div>
        </div>

        <div class="config-field" *ngIf="previewData.data.files && previewData.data.files.length > 0">
          <label>Files Uploaded</label>
          <div class="files-list">
            <div *ngFor="let file of previewData.data.files; let i = index" class="file-item">
              <ava-icon iconName="file-text" [iconColor]="getFileIconColor(i)" [iconSize]="16"></ava-icon>
              <span class="file-name">{{ file.fileName || file.name || "Knowledge Base Data" }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Guardrail Preview -->
    <div *ngIf="previewData?.type === 'guardrail'" class="guardrail-preview">
      <div class="model-section">
        <h3>Guardrail Details</h3>
        
        <div class="model-field" *ngIf="previewData?.title || previewData.data.name">
          <label>Guardrail Name</label>
          <div class="field-value">{{ previewData?.title || previewData.data.name }}</div>
        </div>

        <div class="model-field" *ngIf="previewData.data.description">
          <label>Description</label>
          <div class="field-value description-text">{{ previewData.data.description }}</div>
        </div>

        <div class="model-meta">
          <div class="meta-row">
            <div class="meta-item" *ngIf="previewData.data.createdBy">
              <label>Added by</label>
              <div class="field-value">{{ previewData.data.createdBy}}</div>
            </div>
            <div class="meta-item" *ngIf="previewData.data.createdDate || previewData.data.createdOn">
              <label>Added on</label>
              <div class="field-value">{{ previewData.data.createdDate || previewData.data.createdOn | date:'MM/dd/yyyy' }}</div>
            </div>
          </div>
        </div>
      </div>

      <div class="model-section"  *ngIf="previewData.data.yamlContent || previewData.data.content" >
        <h3>Guardrail Configuration</h3>
        
        <div class="config-field" *ngIf="previewData.data.content">
          <label>Guardrail in Colang</label>
          <div class="code-content">{{ previewData.data.content }}</div>
        </div>
         <div class="config-field" *ngIf="previewData.data.yamlContent">
          <label>Guardrail in Yml</label>
          <div class="code-content">{{ previewData.data.yamlContent }}</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="previewData?.error" class="preview-error">
    <p>{{ previewData.error }}</p>
  </div>
</div>
 <!-- <div panel-footer>
    <ava-button [label]="getButtonLabel()" variant="info" width="100%" (userClick)="onButtonClick($event)"></ava-button>
  </div> -->
</app-preview-panel>
</div>
