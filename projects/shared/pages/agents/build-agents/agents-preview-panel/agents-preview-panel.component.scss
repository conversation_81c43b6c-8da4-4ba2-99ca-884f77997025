
// Main panel structure
.preview-panel {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.panel-container {
  position: relative;
  z-index: 1000;
  width: 480px;
  max-height: 90vh;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  animation: slideIn 0.3s ease-out;
  display: flex;
  flex-direction: column;
}

.preview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

  .panel-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1a1d29;
    margin: 0;
  }
  
  .close-btn {
    width: 32px;
    height: 32px;
    background: transparent;
    border: none;
    color: #6b7280;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      background: #f3f4f6;
      color: #374151;
    }
  }
// Content area
.preview-content {
  position: relative;
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden; /* Prevent horizontal overflow */
  max-height: calc(90vh - 140px);  
  padding-top:40px;
  border-top: 1px solid #e5e7eb;
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 3px;

    &:hover {
      background: rgba(0, 0, 0, 0.5);
    }
  }
}

// Common field styling
.config-field {
  margin-bottom: 20px;
  
  label {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: #1a1d29;
    margin-bottom: 8px;
  }
  
  .field-value {
    font-size: 0.875rem;
    color: #1a1d29;
    line-height: 1.5;
  }
  
  .field-hint {
    font-size: 0.75rem;
    color: #6b7280;
    margin-top: 4px;
  }
}

// Code content styling
.code-content, .prompt-content {
  padding: 16px;
  background: #f8f9fa;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  color: #1a1d29;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
  font-size: 0.875rem;
  line-height: 1.6;
  min-height: 120px;
  white-space: pre-wrap;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: none;
  -ms-overflow-style: none;
  
  &::-webkit-scrollbar {
    display: none;
  }
  
  &:empty::before {
    content: 'No configuration available';
    color: #9ca3af;
    font-style: italic;
  }
}

// Model preview specific
.model-preview {
  .model-section {
    margin-bottom: 32px;
    
    h3 {
      font-size: 1.25rem;
      font-weight: 600;
      color: #1a1d29;
      margin: 0 0 24px 0;
    }
  }

  .config-row {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
    
    .config-field.half-width {
      flex: 1;
      margin-bottom: 0;
      min-width: 0;
    }
  }

  .model-meta {
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 1px solid #e5e7eb;
    
    .meta-row {
      display: flex;
      gap: 10rem;
      
      .meta-item {
        label {
          font-size: 0.875rem;
          color: #1a1d29;
          margin-bottom: 4px;
          font-weight: 600;
        }
        
        .field-value {
          font-weight: 500;
          color: #1a1d29;
          font-size: 0.875rem;
        }
      }
    }
  }
}

// Tool and prompt preview
.tool-preview, .prompt-preview {
  .model-section {
    margin-bottom: 32px;
    
    h3 {
      font-size: 1.25rem;
      font-weight: 600;
      color: #1a1d29;
      margin: 0 0 24px 0;
    }
  }

  .model-field {
    margin-bottom: 24px;
    
    label {
      font-size: 0.875rem;
      font-weight: 600;
      color: #1a1d29;
      margin-bottom: 8px;
    }
    
    .field-value {
      font-size: 1.125rem;
      font-weight: 600;
      color: #1a1d29;
      line-height: 1.5;
      
      &.description-text {
        font-weight: 400;
        color: #6b7280;
        line-height: 1.6;
      }
    }
  }

  .input-display {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: #f8f9fa;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    color: #1a1d29;
    min-height: 48px;
    font-size: 0.875rem;
  }
}

// Footer
::ng-deep .preview-panel [panel-footer] {
  padding: 20px 24px 24px 24px;
  border-top: 1px solid #f0f1f2;
  background: #fafbfc;
  
  ava-button {
    width: 100%;
    
    button {
      width: 100%;
      height: 44px;
      font-weight: 600;
      border-radius: 8px;
      transition: all 0.2s ease;
      
      &:hover {
        transform: translateY(-1px);
        // box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
      }
    }
  }
}

// Loading and error states
.preview-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  
  .loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f4f6;
    border-top: 3px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
  }
  
  p {
    color: #6b7280;
    font-size: 0.875rem;
  }
}

.preview-error {
  text-align: center;
  color: #dc2626;
  padding: 40px 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// Responsive design
@media (max-width: 768px) {
  .panel-container {
    width: 95vw;
    max-width: 400px;
  }
  
  .panel-title {
    font-size: 1.25rem;
  }
  
}


// Type-specific styling
.model-preview .preview-field {
  border-left-color: #10b981;
}

.tool-preview .preview-field {
  border-left-color: #f59e0b;
}

.prompt-preview .preview-field {
  border-left-color: #8b5cf6;
}

.knowledge-preview .preview-field {
  border-left-color: #06b6d4;
}

.guardrail-preview .preview-field {
  border-left-color: #ef4444;
}

.model-preview {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden; /* Prevent horizontal overflow */
  padding-bottom: 20px;
  width: 100%;
  box-sizing: border-box;
  
  .model-section {
    margin-bottom: 32px;
    
    h3 {
      font-size: 1.25rem;
      font-weight: 600;
      color: #1a1d29;
      margin: 0 0 24px 0;
    }
  }

  .config-row {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
    width: 100%;
    
    .config-field.half-width {
      flex: 1;
      margin-bottom: 0;
      min-width: 0;
      
      ::ng-deep ava-textbox {
        width: 100%;
        
        .textbox-container {
          width: 100%;
          
          input {
            width: 100%;
            box-sizing: border-box;
          }
        }
      }
    }
  }

  .config-field {
    margin-bottom: 20px;
    width: 100%;
    box-sizing: border-box;
    
    label {
      display: block;
      font-size: 0.875rem;
      font-weight: 600;
      color: #1a1d29;
      margin-bottom: 8px;
    }
    
    .field-hint {
      font-size: 0.75rem;
      color: #6b7280;
      margin-top: 4px;
    }
    
    ::ng-deep ava-textbox {
      width: 100%;
      
      .textbox-container {
        width: 100%;
        
        input {
          width: 100%;
          box-sizing: border-box;
        }
      }
    }
  }

  .model-field {
    margin-bottom: 24px;
    
    label {
      display: block;
      font-size: 0.875rem;
      font-weight: 500;
      color: #374151;
      margin-bottom: 8px;
    }
    
    .field-value {
      font-size: 1.125rem;
      font-weight: 600;
      color: #1a1d29;
      line-height: 1.5;
      
      &.description-text {
        font-weight: 400;
        color: #6b7280;
        line-height: 1.6;
      }
    }
  }

  .model-meta {
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 1px solid #e5e7eb;
    
    .meta-row {
      display: flex;
      gap: 10rem;
      
      .meta-item {
        label {
          display: block;
          font-size: 0.875rem;
          color: #6b7280;
          margin-bottom: 4px;
          font-weight: 400;
        }
        
        .field-value {
          font-weight: 500;
          color: #1a1d29;
          font-size: 0.875rem;
        }
      }
    }
  }

  .config-field {
    margin-bottom: 20px;
    
    label {
      display: block;
      font-size: 0.875rem;
      font-weight: 500;
      color: #374151;
      margin-bottom: 8px;
    }
    
    .dropdown-display,
    .input-display {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      background: #f8f9fa;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      color: #1a1d29;
      min-height: 48px;
      box-sizing: border-box;
      font-size: 0.875rem;
    }
    
    .dropdown-display {
      &::after {
        content: '';
        width: 0;
        height: 0;
        border-left: 6px solid transparent;
        border-right: 6px solid transparent;
        border-top: 8px solid #6b7280;
        margin-left: 8px;
      }
      
      .engine-icon {
        margin-right: 8px;
        font-size: 1rem;
      }
    }

    // Temperature slider styling
    ::ng-deep ava-slider {
      .slider-container {
        display: flex;
        align-items: center;
        gap: 16px;
        
        .slider-track {
          flex: 1;
          height: 6px;
          background: #e5e7eb;
          border-radius: 3px;
          position: relative;
          
          .slider-fill {
            height: 100%;
            background: #3b82f6;
            border-radius: 3px;
          }
        }
        
        .slider-thumb {
          width: 20px;
          height: 20px;
          background: #3b82f6;
          border-radius: 50%;
          cursor: pointer;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
          border: none;
        }
        
        .slider-value {
          min-width: 60px;
          padding: 8px 12px;
          background: #f8f9fa;
          border: 1px solid #e5e7eb;
          border-radius: 6px;
          text-align: center;
          font-size: 0.875rem;
          color: #374151;
        }
      }
    }
  }

  .config-toggle {
    margin: 16px 0;
    
    .toggle-text {
      display: inline-block;
      cursor: pointer;
      font-size: 14px;
      color: #007bff;
      transition: color 0.2s ease;

      &:hover {
        color: #0056b3;
      }
    }
  }

  // .config-details {
  //   max-height: none; // Remove height restriction
  //   overflow: visible;
  //   transition: max-height 0.3s ease;

  //   &.expanded {
  //     max-height: none; // Remove height restriction when expanded
  //   }
  // }
}
.config-toggle {
  margin: 16px 0;
  
  .toggle-text {
    display: inline-block;
    cursor: pointer;
    font-size: 14px;
    color: #007bff;
    transition: color 0.2s ease;

    &:hover {
      color: #0056b3;
    }
  }
}

.config-details {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease;

  &.expanded {
    max-height: 500px;
  }
}

.prompt-preview {
  .model-section {
    margin-bottom: 32px;
    
    h3 {
      font-size: 1.25rem;
      font-weight: 600;
      color: #1a1d29;
      margin: 0 0 24px 0;
    }
  }

  .model-field {
    margin-bottom: 24px;
    
    label {
      display: block;
      font-size: 0.875rem;
      font-weight: 500;
      color: #374151;
      margin-bottom: 8px;
    }
    
    .field-value {
      font-size: 1.125rem;
      font-weight: 600;
      color: #1a1d29;
      line-height: 1.5;
      
      &.description-text {
        font-weight: 400;
        color: #6b7280;
        line-height: 1.6;
      }
    }
  }

  .model-meta {
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 1px solid #e5e7eb;
    
    .meta-row {
      display: flex;
      gap: 10rem;
      
      .meta-item {
        label {
          display: block;
          font-size: 0.875rem;
          color: #6b7280;
          margin-bottom: 4px;
          font-weight: 400;
        }
        
        .field-value {
          font-weight: 500;
          color: #1a1d29;
          font-size: 0.875rem;
        }
      }
    }
  }

  .config-field {
    margin-bottom: 20px;
    
    label {
      display: block;
      font-size: 0.875rem;
      font-weight: 500;
      color: #374151;
      margin-bottom: 8px;
      
      .required {
        color: #ef4444;
        margin-left: 2px;
      }
    }
    
    .prompt-content {
      padding: 16px;
      background: #f8f9fa;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      color: #1a1d29;
      font-size: 0.875rem;
      line-height: 1.6;
      min-height: 120px;
      white-space: pre-wrap;
    }
  }
}

.guardrail-preview {
  .model-section {
    margin-bottom: 32px;
    
    h3 {
      font-size: 1.25rem;
      font-weight: 600;
      color: #1a1d29;
      margin: 0 0 24px 0;
    }
  }

  .model-field {
    margin-bottom: 24px;
    
    label {
      display: block;
      font-size: 0.875rem;
      font-weight: 600;
      color: #1a1d29;
      margin-bottom: 8px;
    }
    
    .field-value {
      font-size: 0.875rem;
      font-weight: 400;
      color: #1a1d29;
      line-height: 1.5;
      
      &.description-text {
        font-weight: 400;
        color: #6b7280;
        line-height: 1.6;
      }
    }
  }

  .model-meta {
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 1px solid #e5e7eb;
    
    .meta-row {
      display: flex;
      gap: 10rem;
      
      .meta-item {
        label {
          display: block;
          font-size: 0.875rem;
          color: #1a1d29;
          margin-bottom: 4px;
          font-weight: 600;
        }
        
        .field-value {
          font-weight: 500;
          color: #1a1d29;
          font-size: 0.875rem;
        }
      }
    }
  }

  .config-field {
    margin-bottom: 20px;
    
    label {
      display: block;
      font-size: 0.875rem;
      font-weight: 600;
      color: #1a1d29;
      margin-bottom: 8px;
    }
    
    .code-content {
      padding: 16px;
      background: #f8f9fa;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      color: #1a1d29;
      font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
      font-size: 0.875rem;
      line-height: 1.6;
      min-height: 200px;
      white-space: pre-wrap;
      overflow-y: auto;
      max-height: 400px;
    }
  }
}

.knowledge-preview {
  .model-section {
    margin-bottom: 32px;
    
    h3 {
      font-size: 1.25rem;
      font-weight: 600;
      color: #1a1d29;
      margin: 0 0 24px 0;
    }
  }

  .model-field {
    margin-bottom: 24px;
    
    label {
      display: block;
      font-size: 0.875rem;
      font-weight: 600;
      color: #1a1d29;
      margin-bottom: 8px;
    }
    
    .field-value {
      font-size: 1.125rem;
      font-weight: 600;
      color: #1a1d29;
      line-height: 1.5;
      
      &.description-text {
        font-weight: 400;
        color: #6b7280;
        line-height: 1.6;
      }
    }
  }

  .model-meta {
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 1px solid #e5e7eb;
    
    .meta-row {
      display: flex;
      gap: 10rem;
      
      .meta-item {
        label {
          display: block;
          font-size: 0.875rem;
          color: #1a1d29;
          margin-bottom: 4px;
          font-weight: 600;
        }
        
        .field-value {
          font-weight: 500;
          color: #1a1d29;
          font-size: 0.875rem;
        }
      }
    }
  }

  .config-field {
    margin-bottom: 24px;
    
    label {
      display: block;
      font-size: 0.875rem;
      font-weight: 600;
      color: #1a1d29;
      margin-bottom: 8px;
    }
    
    .dropdown-display,
    .input-display {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      background: #f8f9fa;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      color: #1a1d29;
      min-height: 48px;
      box-sizing: border-box;
      font-size: 0.875rem;
    }
    
    .dropdown-display {
      &::after {
        content: '';
        width: 0;
        height: 0;
        border-left: 6px solid transparent;
        border-right: 6px solid transparent;
        border-top: 8px solid #6b7280;
        margin-left: 8px;
      }
    }

    .slider-container {
      display: flex;
      align-items: center;
      gap: 16px;
      
      .slider-track {
        flex: 1;
        height: 6px;
        background: #e5e7eb;
        border-radius: 3px;
        position: relative;
        
        .slider-fill {
          height: 100%;
          background: #3b82f6;
          border-radius: 3px;
        }
      }
      
      .slider-value {
        min-width: 60px;
        padding: 8px 12px;
        background: #f8f9fa;
        border: 1px solid #e5e7eb;
        border-radius: 6px;
        text-align: center;
        font-size: 0.875rem;
        color: #374151;
      }
    }
  }

  .files-list {
    .file-item {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      background: #f8f9fa;
      border-radius: 6px;
      margin-bottom: 8px;
      
      .file-name {
        color: #3b82f6;
        font-size: 0.875rem;
        font-weight: 500;
      }
    }
  }
}

.tool-preview {
  .model-section {
    margin-bottom: 32px;
    
    h3 {
      font-size: 1.25rem;
      font-weight: 600;
      color: #1a1d29;
      margin: 0 0 24px 0;
    }
  }

  .model-field {
    margin-bottom: 24px;
    
    label {
      display: block;
      font-size: 0.875rem;
      font-weight: 600;
      color: #1a1d29;
      margin-bottom: 8px;
    }
    
    .field-value {
      font-size: 1.125rem;
      font-weight: 600;
      color: #1a1d29;
      line-height: 1.5;
      
      &.description-text {
        font-weight: 400;
        color: #6b7280;
        line-height: 1.6;
      }
    }
  }

  .model-meta {
    margin-bottom: 32px;
    padding-bottom: 24px;
    border-bottom: 1px solid #e5e7eb;
    
    .meta-row {
      display: flex;
      gap: 10rem;
      
      .meta-item {
        label {
          display: block;
          font-size: 0.875rem;
          color: #1a1d29;
          margin-bottom: 4px;
          font-weight: 600;
        }
        
        .field-value {
          font-weight: 500;
          color: #1a1d29;
          font-size: 0.875rem;
        }
      }
    }
  }

  .config-field {
    margin-bottom: 20px;
    
    label {
      display: block;
      font-size: 0.875rem;
      font-weight: 600;
      color: #1a1d29;
      margin-bottom: 8px;
    }
    
    .input-display {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      background: #f8f9fa;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      color: #1a1d29;
      min-height: 48px;
      box-sizing: border-box;
      font-size: 0.875rem;
    }
    
    .code-content {
      padding: 16px;
      background: #f8f9fa;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      color: #1a1d29;
      font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
      font-size: 0.875rem;
      line-height: 1.6;
      min-height: 200px;
      white-space: pre-wrap;
      
      // Always show the border and background, even when empty
      &:empty::before {
        content: 'No configuration available';
        color: #9ca3af;
        font-style: italic;
      }
    }
  }
}

// Ensure the panel footer is positioned correctly
[panel-footer] {
  position: sticky;
  bottom: 0;
  background: white;
  padding: 16px;
  border-top: 1px solid #f0f1f2;
  margin-top: auto;
}

.code-content {
  // Hide scrollbar while keeping scroll functionality
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
  
  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Edge */
  }
}

// Remove shadow from textboxes
.config-field {
  ::ng-deep ava-textbox {
    .textbox-container {
      box-shadow: none !important;
      
      input {
        box-shadow: none !important;
      }
    }
  }
}
::ng-deep app-preview-panel .preview-panel > .preview-header {
  padding: 24px !important;
}

::ng-deep app-preview-panel .preview-panel > .preview-content {
  padding: 0 24px !important;
}

.disabled-slider {
  pointer-events: none;
  opacity: 0.6;
  
  ::ng-deep .slider-container {
    cursor: not-allowed;
    
    .slider-thumb {
      cursor: not-allowed;
    }
  }
}
::ng-deep app-loader {
  visibility: hidden !important;
  pointer-events: none !important;
}

