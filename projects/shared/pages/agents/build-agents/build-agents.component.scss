// Top Navigation Bar Styles
.top-nav-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: var(--build-agents-nav-bg);
  border-bottom: 1px solid var(--build-agents-nav-border);
  height: 49px;
  border-radius: 8px;
  position: sticky;
  top: 0;
  z-index: 100;

  .nav-left {
    display: flex;
    align-items: center;
    gap: 8px;

    .back-button {
      display: flex;
      align-items: center;
      gap: 8px;
      background: none;
      border: none;
      color: var(--build-agents-back-btn-text);
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      padding: 8px 12px;
      border-radius: 6px;
      transition: all 0.2s ease;

      // Ensure ava-icon is visible
      ava-icon {
        display: inline-block;
        width: 16px;
        height: 16px;
        color: #374151;
        min-width: 16px;
        min-height: 16px;
        opacity: 1;
        visibility: visible;
      }

      // Ensure SVG icon is visible
      svg {
        display: inline-block;
        width: 16px;
        height: 16px;
        min-width: 16px;
        min-height: 16px;
        opacity: 1;
        visibility: visible;
      }

      &:hover {
        background-color: var(--build-agents-back-btn-hover-bg);
        color: var(--build-agents-back-btn-hover-text);
      }

      span {
        font-size: 16px;
        font-weight: 600;
      }
    }
  }

  .nav-center {
    .agent-type-toggle {
      display: flex;
      background: var(--build-agents-toggle-bg);
      border-radius: 8px;
      padding: 2px;
      gap: 2px;

      .toggle-btn {
        padding: 4px 12px;
        background: transparent;
        border: none;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        color: var(--build-agents-toggle-btn-text);
        cursor: pointer;
        transition: all 0.2s ease;
        white-space: nowrap;

        &:hover {
          color: var(--build-agents-toggle-btn-hover);
        }

        &.active {
          background: var(--build-agents-toggle-active-bg);
          color: var(--build-agents-toggle-active-text);
          box-shadow: var(--build-agents-toggle-shadow);
        }
      }
    }
  }

  .nav-right {
    // Save button styles are handled by ava-button component
  }
}

// Agent Details Floater Styles
.agent-details-floater {
  position: absolute;
  top: 6.5%;
  left: 30rem;
  width: 320px;
  background: var(--build-agents-floater-bg);
  border: 1px solid var(--build-agents-floater-border);
  border-radius: 8px;
  box-shadow: var(--build-agents-floater-shadow);
  z-index: 50;
  transition: all 0.3s ease;

  &.pulsating {
    animation: pulsatingBorder 2s ease-in-out infinite;
  }

  &.collapsed {
    .floater-content {
      display: none;
    }
  }

  .floater-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid var(--build-agents-floater-header-border);
    cursor: pointer;
    user-select: none;
    transition: background-color 0.2s ease;
    border-radius: inherit;
    background-color: var(--build-agents-floater-header-bg);

    &:hover {
      background-color: var(--build-agents-floater-header-hover);
    }

    h4 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--build-agents-floater-title);
    }

    ava-icon {
      transition: transform 0.3s ease;
    }
  }

  .floater-content {
    padding: 20px;

    .form-fields {
      display: flex;
      flex-direction: column;
      gap: 16px;

      .field-group {
        .field-label {
          display: block;
          font-size: 14px;
          font-weight: 500;
          color: var(--build-agents-field-label);
          margin-bottom: 6px;
        }

        .agent-name-field,
        .agent-description-field {
          width: 100%;

          ::ng-deep .ava-textbox,
          ::ng-deep .ava-textarea {
            width: 100%;

            input,
            textarea {
              width: 100%;
              border-radius: 6px;
              padding: 8px 12px;
              font-size: 14px;

              &:focus {
                // border-color: #2563eb;
                outline: none;
                // box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
              }
            }

            textarea {
              min-height: 80px;
              resize: vertical;
            }
          }
        }
      }
    }
  }
}

@mixin hide-scrollbar {
  &::-webkit-scrollbar {
    display: none;
    width: 0;
    height: 0;
  }
  scrollbar-width: none;
  -ms-overflow-style: none;
}

// Global tooltip scroll fix - ensure all tooltips can scroll properly
::ng-deep {
  // Fix for ava-tooltip component scrolling issues
  ava-tooltip,
  .ava-tooltip,
  [class*="tooltip"] {
    .tooltip-content,
    .tooltip-body,
    .tooltip-text {
      // Remove any text truncation that might be applied globally
      text-overflow: clip !important;
      -webkit-line-clamp: unset !important;
      line-clamp: unset !important;
      -webkit-box-orient: unset !important;
      display: block !important;

      // Enable scrolling for long content
      max-height: 300px !important;
      overflow-y: auto !important;
      overflow-x: hidden !important;
      white-space: pre-line !important;
      word-wrap: break-word !important;

      // Ensure scrollbar is visible
      scrollbar-width: thin;
      scrollbar-color: rgba(0, 0, 0, 0.3) rgba(0, 0, 0, 0.1);

      &::-webkit-scrollbar {
        width: 6px !important;
        height: 6px !important;
      }

      &::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.1) !important;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.3) !important;
        border-radius: 3px;

        &:hover {
          background: rgba(0, 0, 0, 0.5) !important;
        }
      }
    }
  }

  // Specific fix for any tooltip that might have fixed height restrictions
  .tooltip-wrapper,
  .tooltip-container {
    max-height: none !important;
    height: auto !important;
  }
}

.build-agents-container {
  height: 89vh;
  display: flex;
  flex-direction: column;
  background-color: var(--background-primary);
  overflow: hidden;

  // Top-level page title styling
  > h2 {
    margin: 0;
    padding: 1rem 1.5rem;
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary);
    background-color: var(--background-primary);
    border-bottom: 1px solid var(--border-color);
    flex-shrink: 0;
  }

  .header-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 60px;
    background-color: var(--card-bg);
    border-bottom: 1px solid var(--border-color);

    .breadcrumb {
      display: flex;
      align-items: center;
      gap: 8px;
      flex: 1;

      .nav-item {
        font-size: 14px;
        color: var(--text-secondary);
        cursor: pointer;
        transition: color 0.2s ease;

        &:hover {
          color: var(--text-primary);
        }

        &.active {
          color: var(--text-primary);
          font-weight: 500;
        }
      }

      .separator {
        color: var(--text-tertiary);
        font-size: 14px;
      }

      .close-btn {
        background: none;
        border: none;
        color: var(--text-secondary);
        cursor: pointer;
        padding: 4px;
        // border-radius: 4px;
        margin-left: 16px;
        transition: all 0.2s ease;

        &:hover {
          background-color: var(--hover-bg);
          color: var(--text-primary);
        }
      }
    }

    .header-actions {
      .action-group {
        display: flex;
        align-items: center;
        gap: 8px;

        .action-btn {
          background: none;
          border: 1px solid var(--border-color);
          color: var(--text-secondary);
          cursor: pointer;
          padding: 8px;
          // border-radius: 6px;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            background-color: var(--hover-bg);
            border-color: var(--border-hover);
            color: var(--text-primary);
          }
        }

        .run-btn {
          background: var(--dashboard-primary);
          border: 1px solid var(--dashboard-primary);
          color: white;
          cursor: pointer;
          padding: 8px 16px;
          // border-radius: 6px;
          font-size: 14px;
          font-weight: 500;
          display: flex;
          align-items: center;
          gap: 6px;
          transition: all 0.2s ease;

          &:hover {
            background: var(--dashboard-primary-hover);
            border-color: var(--dashboard-primary-hover);
          }
        }
      }
    }
  }

  .main-content {
    display: flex;
    flex-direction: column; // Changed from row to column
    flex: 1;
    overflow: hidden;
    height: calc(100vh - 60px); // Full height minus header
    max-height: calc(100vh - 60px);
    position: relative;

    .canvas-area {
      flex: 1;
      background-color: var(--background-primary);
      overflow: hidden;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative; // Added for absolute positioning of panel

      // Configure Agent Floating Panel - Positioned on canvas area
      .configure-agent-panel {
        position: absolute;
        top: 20px;
        left: 20px;
        width: 380px; // Increased width to match design
        height: 80vh;
        margin-top: 2rem;
        margin-bottom: 2rem;
        background-color: var(--build-agents-panel-bg); // Pure white background
        border: 1px solid var(--build-agents-panel-border);
        border-radius: 8px; // More rounded corners
        box-shadow: var(--build-agents-panel-shadow); // Softer shadow
        z-index: 20; // Lower z-index so agent name section can appear beside it
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        overflow: hidden;
        display: flex;
        flex-direction: column; // Use flexbox for proper height distribution

        // Hide panel in execute mode
        .main-content.execute-mode & {
          display: none;
        }

        // Collapsed state
        &.collapsed {
          width: auto;
          min-width: 200px;
          height: auto;

          .panel-header {
            padding: 12px 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            background-color: var(--build-agents-panel-header-bg);
            border-bottom: none;

            h3 {
              margin: 0;
              font-size: 14px;
              font-weight: 600;
              color: var(--text-primary);
            }

            ava-icon {
              transition: transform 0.3s ease;
            }
          }

          .panel-content {
            display: none;
          }
        }

        .panel-header {
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: space-between;
          background-color: var(--build-agents-panel-header-bg);
          transition: all 0.3s ease;

          &:hover {
            background-color: var(--build-agents-panel-header-hover);
          }

          h3 {
            margin: 0;
            font-size: 20px;
            font-weight: 700;
            color: var(--build-agents-panel-title);
          }

          ava-icon {
            transition: transform 0.3s ease;
          }
        }

        .panel-content {
          margin-top: 1rem;
          flex: 1; // Take remaining height
          overflow: hidden;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          display: flex;
          flex-direction: column;

          &.hidden {
            max-height: 0;
            opacity: 0;
          }

          .tools-section {
            display: flex;
            flex-direction: column;
            height: 100%; // Full height
            overflow: hidden;

            .custom-tabs-container {
              flex-shrink: 0; // Don't shrink tabs

              .builder-custom-tabs {
                @include hide-scrollbar;
              }

              // Custom Tabs Container Styles
              .custom-tabs-wrapper {
                display: flex;
                width: 100%;
                gap: 8px; // Consistent gap
                justify-content: space-between;
                padding: 0;
                min-width: 0; // Allow shrinking

                .tab-item {
                  flex: 1;
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;
                  padding: 12px 8px; // Adjusted padding
                  border: none;
                  background-color: transparent;
                  cursor: pointer;
                  min-height: 56px; // Fixed height
                  min-width: 0; // Allow shrinking
                  transition: all 0.2s ease; // Smooth transitions
                  // border-radius: 8px; // Rounded corners

                  // No hover effects for cleaner design

                  &.active {
                    .tab-icon-box {
                      background-color: var(--build-agents-tab-active-bg); // Blue background for active
                      border-color: var(--build-agents-tab-active-border);

                      lucide-icon {
                        color: var(--build-agents-tab-active-icon) !important; // White icon
                      }
                    }

                    // Keep label black even when active
                    .tab-label {
                      color: var(--build-agents-tab-active-label);
                      font-weight: 600;
                    }
                  }

                  &.disabled {
                    opacity: 0.5;
                    cursor: not-allowed;
                  }

                  .tab-icon-box {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 32px; // Smaller size
                    height: 32px; // Smaller size
                    border: 1px solid var(--build-agents-tab-border);
                    // border-radius: 8px; // More rounded
                    background-color: var(--build-agents-tab-bg); // Light grey background
                    margin-bottom: 6px; // More spacing

                    .tab-png-icon {
                      width: 16px; // Smaller icon
                      height: 16px; // Smaller icon
                      object-fit: contain;
                    }

                    lucide-icon {
                      width: 16px; // Smaller icon
                      height: 16px; // Smaller icon
                      color: var(--build-agents-tab-icon); // Grey color
                    }
                  }

                  .tab-label {
                    font-size: 12px; // Smaller font
                    font-weight: 500;
                    color: var(--build-agents-tab-label); // Grey color
                    text-align: center;
                    line-height: 1.2;
                  }
                }
              }
            }

            .search-section {
              margin-top: 1rem;
              flex-shrink: 0; // Don't shrink search

              form {
                width: 100%;
              }

              // Search input styling
              ::ng-deep ava-textbox {
                .textbox-container {
                  // border-radius: 12px; // More rounded
                  border: 1px solid var(--build-agents-search-border);
                  background-color: var(--build-agents-search-bg);

                  &:focus-within {
                    border-color: var(--build-agents-search-focus-border);
                    box-shadow: var(--build-agents-search-focus-shadow);
                  }

                  input {
                    background-color: transparent;
                    color: var(--build-agents-search-text);
                    font-size: 14px;

                    &::placeholder {
                      color: var(--build-agents-search-placeholder);
                    }
                  }
                }
              }
            }

            .tools-list {
              flex: 1; // Take remaining space
              overflow-y: auto; // Make scrollable
              overflow-x: hidden;
              @include hide-scrollbar;
              min-height: 0; // Important for flex child to scroll

              .tool-item {
                border: 1px solid var(--build-agents-tool-border);
                background-color: var(--build-agents-tool-bg);
                border-radius: 8px;
                padding: 16px;
                margin-top: 16px;
                cursor: grab;
                transition: all 0.2s ease;
                box-shadow: var(--build-agents-tool-shadow);
                box-shadow: var(--build-agents-tool-shadow-alt);

                &:hover {
                  border-color: var(--build-agents-tool-hover-border);
                  box-shadow: var(--build-agents-tool-hover-shadow);
                  transform: translateY(-1px);
                }

                &:active {
                  cursor: grabbing;
                }

                .tool-header {
                  display: flex;
                  align-items: center;
                  gap: 12px; // More spacing
                  margin-bottom: 8px; // More spacing

                  .tool-icon-box {
                    width: 36px;
                    height: 36px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-shrink: 0;
                    background: var(--build-agents-tool-icon-bg);
                    border-radius: 999px;

                    ava-icon {
                      color: var(--build-agents-tool-icon); // White icon
                    }
                  }

                  .tool-name {
                    margin: 0;
                    font-size: 16px;
                    font-weight: 600;
                    color: var(--build-agents-tool-name) !important;
                    flex: 1;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                  }

                  .tool-count {
                    display: flex;
                    align-items: center;
                    gap: 4px;
                    font-weight: 600;
                    font-size: 16px;
                    color: var(--build-agents-tool-count);

                    .count-text {
                      font-weight: 500;
                    }
                  }
                }

                .tool-description {
                  margin: 0 0 16px 0; // More spacing
                  font-size: 14px; // Larger font
                  color: var(--build-agents-tool-description);
                  line-height: 1.5; // Better line height
                  display: -webkit-box;
                  -webkit-line-clamp: 2;
                  -webkit-box-orient: vertical;
                  overflow: hidden;
                }

                .tool-actions {
                  display: flex;
                  justify-content: flex-end;

                  .preview-btn {
                    font-size: 16px;
                    // &:hover {
                    //   background-color: #e5e7eb;
                    // }
                  }
                }
              }

              .no-results-message {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 2rem 1rem;
                text-align: center;

                .no-results-content {
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  gap: 0.5rem;

                  p {
                    margin: 0;
                    color: #6b7280; // Grey color
                    font-size: 14px;
                  }
                }
              }
            }

            .create-tool-section {
              margin-top: 16px;
              border-top: 1px solid var(--build-agents-panel-border-light); // Lighter border
              background-color: var(--build-agents-panel-bg);
              flex-shrink: 0; // Don't shrink create section

              // Create button styling
              ::ng-deep ava-button {
                .button-container {
                  // border-radius: 12px; // More rounded
                  background: var(--build-agents-create-gradient); // Gradient
                  border: none;
                  font-weight: 600;
                  font-size: 14px;
                  padding: 12px 20px; // More padding

                  &:hover {
                    background: var(--build-agents-create-hover-gradient);
                    transform: translateY(-1px);
                    box-shadow: var(--build-agents-create-shadow);
                  }
                }
              }
            }
          }
        }
      }

      .editor-canvas {
        height: 100%;
        display: flex;
        flex-direction: column;

        h2 {
          margin: 0;
          padding: 1rem;
          font-size: 18px;
          font-weight: 600;
          color: var(--text-primary);
          border-bottom: 1px solid var(--border-color);
        }

        app-canvas-board,
        app-drop-zone-canvas {
          flex: 1;
          overflow: hidden;
        }

        // Canvas board and drop zone specific styles
        ::ng-deep app-canvas-board,
        ::ng-deep app-drop-zone-canvas {
          height: 100%;
          width: 100%;

          .canvas-container {
            height: 100%;
            background-color: var(--background-secondary);
          }

          .canvas-viewport {
            height: 100%;
          }

          .fallback-message {
            color: var(--text-tertiary);
            font-size: 16px;
            text-align: center;
            padding: 40px;
          }

          .floating-toolbar {
            .primary-btn {
              background: linear-gradient(
                45deg,
                lightblue,
                darkblue
              ) !important;
            }
          }

          // Enhanced connection lines styling
          .canvas-edges {
            .edge-path {
              stroke: var(--build-agents-canvas-edge); // Match marker color for consistency
              stroke-width: 1.5px; // Thinner lines as requested
              fill: none;
              transition: none; // Remove transition for smooth movement

              &:hover {
                stroke: var(--build-agents-canvas-edge); // Keep same color on hover
                stroke-width: 2px; // Slightly thicker on hover
                transition: stroke-width 0.1s ease; // Only transition hover effects
              }
            }

            .edge-marker {
              fill: var(--build-agents-canvas-marker); // Match connection line color
              stroke: none;
            }

            .arrow-head {
              fill: var(--build-agents-canvas-arrow); // Match connection line color exactly
            }

            // Animated flow effect
            .edge-animated {
              stroke-dasharray: 5;
              animation: dash 1s linear infinite;
            }
          }

          // Enhanced node styling
          .canvas-nodes {
            .node {
              filter: drop-shadow(var(--build-agents-canvas-node-shadow));
              transition: all 0.3s ease;

              &:hover {
                filter: drop-shadow(var(--build-agents-canvas-node-hover-shadow));
                transform: translateY(-2px);
              }

              .node-content {
                // border-radius: 16px;
                border: 2px solid var(--build-agents-canvas-node-border);
                background: var(--build-agents-canvas-node-bg);
                transition: all 0.3s ease;

                &:hover {
                  border-color: var(--build-agents-canvas-node-hover-border);
                  background: var(--build-agents-canvas-node-hover-bg);
                }
              }

              &.selected .node-content {
                border-color: var(--build-agents-canvas-node-selected-border);
                background: var(--build-agents-canvas-node-selected-bg);
                box-shadow: var(--build-agents-canvas-node-selected-shadow);
              }
            }
          }

          // Agent details dropdown styling (only for build mode, not execute mode)
          &:not(.execute-mode) .header-inputs-section {
            position: absolute;
            top: 52px !important;
            left: 420px !important;
            z-index: 20;
            display: flex;
            flex-direction: column;
            gap: 12px;
            max-width: calc(100% - 440px);

            .agent-details-dropdown {
              min-width: 300px !important;
              max-width: 400px; // Limit maximum width
              transition: min-width 0.3s ease;

              ::ng-deep .ava-dropdown {
                min-width: 300px !important;
                max-width: 400px; // Limit maximum width

                .dropdown-trigger {
                  min-width: 300px !important;
                  max-width: 400px; // Limit maximum width
                  padding: 12px 16px;
                  // border-radius: 12px;
                  border: 2px solid var(--build-agents-dropdown-border);
                  background: var(--build-agents-dropdown-bg);
                  transition: all 0.3s ease;
                  box-shadow: var(--build-agents-dropdown-shadow);

                  &:hover {
                    border-color: var(--build-agents-dropdown-hover-border);
                    box-shadow: var(--build-agents-dropdown-hover-shadow);
                  }

                  &:focus {
                    border-color: var(--build-agents-dropdown-focus-border);
                    box-shadow: var(--build-agents-dropdown-focus-shadow);
                  }
                }

                .dropdown-content {
                  min-width: 300px !important;
                  max-width: 400px; // Limit maximum width
                  // border-radius: 12px;
                  border: 2px solid var(--build-agents-dropdown-border);
                  box-shadow: var(--build-agents-dropdown-shadow);
                  backdrop-filter: blur(8px);
                }
              }
            }

            .agent-name-input {
              max-width: 400px; // Limit maximum width

              ::ng-deep .ava-textbox {
                max-width: 400px; // Limit maximum width

                input {
                  // border-radius: 12px;
                  border: 2px solid var(--build-agents-dropdown-border);
                  transition: all 0.3s ease;
                  box-shadow: var(--build-agents-dropdown-shadow);

                  &:hover {
                    border-color: var(--build-agents-dropdown-hover-border);
                    box-shadow: var(--build-agents-dropdown-hover-shadow);
                  }

                  &:focus {
                    border-color: var(--build-agents-dropdown-focus-border);
                    box-shadow: var(--build-agents-dropdown-focus-shadow);
                  }
                }
              }
            }
          }

          // Canvas container adjustments to prevent overlap with Configure Agent panel (NOT in execute mode)
          .canvas-container {
            margin-left: 420px; // Add left margin to prevent overlap with Configure Agent panel
            width: calc(100% - 420px); // Adjust width to account for the margin
            height: 100%;
            position: relative;

            // In execute mode, remove the margin that pushes content right
            .main-content.execute-mode & {
              margin-left: 0 !important;
              width: 100% !important;
            }
          }

          // Canvas tools toolbar positioning to avoid overlap
          .canvas-tools-toolbar {
            position: absolute;
            bottom: 50px;
            right: 20px;
            width: fit-content;
            z-index: 15; // Lower than Configure Agent panel but higher than canvas content
          }
          .floating-toolbar {
            position: absolute;
            top: 52px !important;
            right: 20px !important;
            z-index: 15;
          }
        }

        // Execute mode specific styles
        &.execute-mode {
          ::ng-deep app-canvas-board {
            // SIMPLE execute mode - no positioning overrides
            width: 100%;
            height: 100%;

            // Remove ALL positioning overrides - let the component handle it
            .canvas-container {
              margin-left: 0 !important; // Remove the margin that was pushing nodes right
              width: 100% !important;
            }

            .canvas-viewport {
              width: 100% !important;
              height: 100% !important;
            }

            .nodes-container {
              width: 100% !important;
              height: 100% !important;
            }

            // Force connections to be visible in execute mode
            .connections-layer {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              z-index: 1;
              pointer-events: none;

              svg {
                width: 100%;
                height: 100%;
                position: absolute;
                top: 0;
                left: 0;

                path {
                  stroke: var(--build-agents-execute-connection) !important;
                  stroke-width: 2px !important;
                  fill: none !important;
                  opacity: 1 !important;
                }

                .arrow-head {
                  fill: var(--build-agents-execute-arrow) !important;
                  opacity: 1 !important;
                }
              }
            }

            .floating-toolbar {
              // Keep execute button visible in execute mode
              position: absolute;
              top: 20px;
              right: 20px;
              z-index: 1000;
            }

            .header-inputs-section {
              display: block;
              position: absolute;
              top: 20px;
              left: 20px;
              z-index: 1000;

              .agent-name-input {
                width: 180px !important;

                ::ng-deep .ava-textbox {
                  width: 180px !important;
                }
              }

              // In execute mode, set dropdown to 76% width
              .agent-details-dropdown {
                width: 76% !important;
                min-width: 200px !important;

                ::ng-deep .ava-dropdown {
                  width: 100% !important;
                  min-width: 200px !important;

                  .dropdown-trigger {
                    width: 100% !important;
                    min-width: 200px !important;
                    padding: 8px 12px;
                  }

                  .dropdown-content {
                    width: 100% !important;
                    min-width: 200px !important;
                  }
                }
              }
            }
          }
        }
      }
    }

    // Execute mode - when in execute mode, show playground area
    &.execute-mode {
      flex-direction: row; // Change to row layout for side-by-side

      .canvas-area {
        flex: 0 0 50%; // Canvas takes 50% width in execute mode
        min-width: 400px; // Ensure minimum width for visibility
        height: 100%; // Full height
        display: flex;
        flex-direction: column; // Stack canvas board properly
        justify-content: flex-start;
        align-items: stretch;
        // border-right: 1px solid #e5e7eb; // Removed border between canvas and playground
        overflow: hidden; // Prevent overflow
        position: relative; // For proper positioning

        // Ensure canvas board is visible in execute mode
        .editor-canvas {
          flex: 1; // Take remaining space
          height: 100%; // Full height
          width: 100%; // Full width
          position: relative;
          overflow: hidden;

          ::ng-deep app-canvas-board {
            width: 100%;
            height: 100%;
            display: block; // Ensure it's displayed
          }
        }
      }

      .playground-area {
        flex: 0 0 50%; // Playground takes 50% width in execute mode
        position: relative;
        background-color: var(--card-bg);
        height: 100%; // Full height
        overflow: hidden;
        display: flex;
        flex-direction: column;
        padding: 10px;
        // border: 1px solid #dcdcdc;
        // border-radius: 16px;
        margin: 10px;
        margin-left: 0; // Remove left margin since we have border

        .exit-execute-btn {
          background: none;
          border: none;
          cursor: pointer;
          padding: 4px;
          // border-radius: 4px;
          color: var(--text-secondary);
          transition: all 0.2s ease;

          &:hover {
            background-color: var(--background-secondary);
            color: var(--text-primary);
          }

          svg {
            width: 16px;
            height: 16px;
          }
        }

        ::ng-deep app-playground {
          flex: 1;
          height: calc(100% - 48px);
          width: 100%;

          .playground-container {
            height: 100%;
            // border-radius: 0;
          }
        }
      }
    }
  }
}

// Preview Panel Styles
.preview-panel {
  position: fixed;
  top: 0;
  right: -400px;
  width: 400px;
  height: 100vh;
  background-color: var(--card-bg);
  border-left: 1px solid var(--border-color);
  box-shadow: -4px 0 16px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;

  &.visible {
    right: 0;
  }

  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--background-primary);

    .preview-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--text-primary);
    }

    .close-preview-btn {
      background: none;
      border: none;
      color: var(--text-secondary);
      cursor: pointer;
      padding: 4px;
      // border-radius: 4px;
      transition: all 0.2s ease;

      &:hover {
        background-color: var(--hover-bg);
        color: var(--text-primary);
      }
    }
  }

  .preview-content {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    @include hide-scrollbar;

    .preview-loading {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 2rem;
      text-align: center;

      .loading-spinner {
        width: 32px;
        height: 32px;
        border: 3px solid var(--border-color);
        border-top: 3px solid var(--dashboard-primary);
        // border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 1rem;
      }

      p {
        margin: 0;
        color: var(--text-secondary);
        font-size: 14px;
      }
    }

    .preview-error {
      padding: 1rem;
      text-align: center;

      p {
        margin: 0;
        color: #ef4444;
        font-size: 14px;
      }
    }

    .preview-field {
      margin-bottom: 1rem;

      label {
        display: block;
        font-size: 12px;
        font-weight: 600;
        color: var(--text-secondary);
        margin-bottom: 0.25rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      span {
        display: block;
        font-size: 14px;
        color: var(--text-primary);
        line-height: 1.4;
      }

      .content-preview {
        background-color: var(--background-secondary);
        border: 1px solid var(--border-color);
        // border-radius: 4px;
        padding: 0.75rem;
        font-size: 12px;
        color: var(--text-primary);
        white-space: pre-wrap;
        word-break: break-word;
        max-height: 200px;
        overflow-y: auto;
        margin: 0;
      }
    }
  }
}

// Animation for loading spinner
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Animation for edge flow
@keyframes dash {
  to {
    stroke-dashoffset: -10;
  }
}

// Additional smooth transitions for nodes
@keyframes node-pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

@keyframes connection-glow {
  0%,
  100% {
    filter: drop-shadow(0 2px 4px rgba(79, 70, 229, 0.2));
  }
  50% {
    filter: drop-shadow(0 4px 8px rgba(79, 70, 229, 0.4));
  }
}

/* Agent Playground Controls */
.agent-playground-controls {
  background: var(--build-agents-playground-bg);
  border: 1px solid var(--build-agents-playground-border);
  // border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;

  .agent-info {
    margin-bottom: 12px;

    h4 {
      margin: 0;
      color: var(--build-agents-playground-title);
      font-size: 16px;
      font-weight: 600;
    }
  }

  .agent-toggles {
    display: flex;
    gap: 16px;
    align-items: center;
    margin-bottom: 12px;
    flex-wrap: wrap;

    .toggle-row {
      display: flex;
      align-items: center;
      min-height: 32px;
    }

    .clear-chat-btn {
      background: var(--build-agents-clear-chat-bg);
      color: white;
      border: none;
      padding: 6px 12px;
      // border-radius: 4px;
      font-size: 12px;
      cursor: pointer;
      transition: background-color 0.2s ease;

      &:hover {
        background: var(--build-agents-clear-chat-hover);
      }
    }
  }

  .agent-file-upload {
    .file-upload-btn {
      background: var(--build-agents-file-upload-bg);
      color: white;
      border: none;
      padding: 8px 16px;
      // border-radius: 4px;
      font-size: 14px;
      cursor: pointer;
      transition: background-color 0.2s ease;

      &:hover {
        background: var(--build-agents-file-upload-hover);
      }
    }

    .agent-uploaded-files {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-top: 8px;

      .file-item {
        display: flex;
        align-items: center;
        background: var(--build-agents-file-item-bg);
        border: 1px solid var(--build-agents-file-item-border);
        // border-radius: 4px;
        padding: 4px 8px;
        font-size: 12px;
        max-width: 200px;

        .file-name {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-right: 8px;
        }

        .remove-file {
          background: none;
          border: none;
          color: var(--build-agents-file-remove);
          cursor: pointer;
          font-size: 16px;
          line-height: 1;
          padding: 0;
          width: 16px;
          height: 16px;
          display: flex;
          align-items: center;
          justify-content: center;

          &:hover {
            color: var(--build-agents-file-remove-hover);
          }
        }
      }
    }
  }
}

// API Response Modal Styling
.modal-content-wrapper {
  padding: 16px 0;

  p {
    margin: 0;
    line-height: 1.5;
    font-size: 14px;
    color: var(--text-primary);
    white-space: pre-wrap; // Preserve line breaks in JSON responses
  }

  &.error-content {
    .error-text {
      color: var(--build-agents-modal-error); // Red color for errors
      font-weight: 500;
    }
  }
}

::ng-deep .tools-section .ava-button.secondary {
  --button-effect-color: var(--button-variant-secondary-effect-color);
  border-radius: 999px !important;
  background-color: var(--Global-colors-Royal-blue-50, #e9effd) !important;
  border: none !important;
}

// Pulsating animation for Agent Details floater
@keyframes pulsatingBorder {
  0% {
    border-color: var(--build-agents-pulsating-border);
    box-shadow: var(--build-agents-pulsating-shadow);
  }
  50% {
    border-color: var(--build-agents-pulsating-active-border);
    box-shadow: var(--build-agents-pulsating-active-shadow), var(--build-agents-pulsating-active-ring);
  }
  100% {
    border-color: var(--build-agents-pulsating-border);
    box-shadow: var(--build-agents-pulsating-shadow);
  }
}

::ng-deep .tool-icon-box svg {
  stroke: var(--build-agents-tool-icon-stroke);
}

::ng-deep .tab-item.active .tab-icon-box svg {
  stroke: var(--build-agents-tool-icon-stroke);
}

::ng-deep .ava-button.primary.ava-button--glass-10{
  background: var(--button-primary-glass-10-background) !important;
}
