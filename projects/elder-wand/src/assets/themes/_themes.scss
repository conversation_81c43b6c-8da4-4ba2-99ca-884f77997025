/**
* =========================================================================
* Play+ Design System: Elder Wand Theme
*
* Elder Wand theme overrides for semantic tokens.
* This theme uses Red, Purple, and Violet color families for a modern design.
* =========================================================================
*/

[data-theme="light"] {
  /* --- Elder Wand Theme Color Overrides --- */
 
  --background-image: url('../../../public/background.jpg');
  
  /* =======================
   RED COLOR FAMILY
   ======================= */
  --red-50: #fde9ef;
  --red-100: #f8b9cf;
  --red-200: #f598b7;
  --red-300: #f06896;
  --red-400: #ed4b82;
  --red-500: #e91e63;
  --red-600: #d41b5a;
  --red-700: #a51546;
  --red-800: #801136;
  --red-900: #620d2a;

  /* =======================
   PURPLE COLOR FAMILY
   ======================= */
  --purple-50: #f5e9f7;
  --purple-100: #e0bce7;
  --purple-200: #d19cdb;
  --purple-300: #bd6eca;
  --purple-400: #b052c0;
  --purple-500: #9c27b0;
  --purple-600: #8e23a0;
  --purple-700: #6f1c7d;
  --purple-800: #561561;
  --purple-900: #42104a;

  /* =======================
   VIOLET COLOR FAMILY
   ======================= */
  --violet-50: #f0ebf8;
  --violet-100: #d0c2e9;
  --violet-200: #b9a4de;
  --violet-300: #997bcf;
  --violet-400: #8561c5;
  --violet-500: #673ab7;
  --violet-600: #5e35a7;
  --violet-700: #492982;
  --violet-800: #392065;
  --violet-900: #2b184d;

  /* =======================
   PRIMARY COLORS (Using Red as Primary)
   ======================= */
  --color-brand-primary: var(--red-500);
  --color-brand-primary-hover: var(--red-600);
  --color-brand-primary-active: var(--red-700);
  --color-surface-interactive-primary: var(--red-500);
  --color-surface-interactive-primary-hover: var(--red-600);
  --color-surface-interactive-primary-active: var(--red-700);
  --color-border-primary: var(--red-500);
  --color-border-primary-hover: var(--red-600);
  --color-border-primary-active: var(--red-700);
  --color-text-primary: #374151;
  --color-text-on-primary: #ffffff;

  /* =======================
   SECONDARY COLORS (Using Purple as Secondary)
   ======================= */
  --color-brand-secondary: var(--purple-500);
  --color-brand-secondary-hover: var(--purple-600);
  --color-brand-secondary-active: var(--purple-700);
  --color-surface-interactive-secondary: var(--purple-500);
  --color-surface-interactive-secondary-hover: var(--purple-600);
  --color-surface-interactive-secondary-active: var(--purple-700);
  --color-border-secondary: var(--purple-500);
  --color-border-secondary-hover: var(--purple-600);
  --color-border-secondary-active: var(--purple-700);
  --color-text-secondary: var(--purple-500);
  --color-text-on-secondary: #ffffff;
  --color-background-secondary: var(--red-50);

  /* =======================
   ACCENT COLORS (Using Violet as Accent)
   ======================= */
  --color-accent: var(--violet-500);
  --color-accent-hover: var(--violet-600);
  --color-accent-active: var(--violet-700);
  --color-surface-interactive-accent: var(--violet-500);
  --color-surface-interactive-accent-hover: var(--violet-600);
  --color-surface-interactive-accent-active: var(--violet-700);

  /* =======================
   BUTTONS, TABS, TAGS: Use these tokens for all secondary/primary states
   ======================= */
  --color-text-placeholder: #9ca3af;
  --color-text-disabled: #9ca3af;
  --color-text-on-brand: #ffffff;
  --color-text-interactive: var(--red-500);
  --color-text-interactive-hover: var(--red-600);
  --color-text-success: #10b981;
  --color-text-error: var(--red-500);
  --color-background-primary: #ffffff;
  --color-background-disabled: #f3f4f6;
  --color-surface-interactive-default: var(--red-500);
  --color-surface-interactive-hover: var(--red-600);
  --color-surface-interactive-active: var(--red-700);
  --color-surface-disabled: #e5e7eb;
  --color-surface-subtle-hover: #f9fafb;
  --color-border-default: #d1d5db;
  --color-border-subtle: #e5e7eb;
  --color-border-interactive: var(--red-500);
  --color-border-focus: var(--red-500);
  --color-border-error: var(--red-500);
  --color-background-error: var(--red-500);

  /* =======================
   SEMANTIC BORDER COLORS
   ======================= */
  --color-border-warning: #f59e0b;
  --color-border-success: #10b981;
  --color-border-info: #3b82f6;

  /* =======================
   SEMANTIC TEXT COLORS
   ======================= */
  --color-text-warning: #d97706;
  --color-text-success: #059669;
  --color-text-info: #2563eb;

  /* =======================
   SEMANTIC BACKGROUND COLORS
   ======================= */
  --color-background-warning: #fef3c7;
  --color-background-success: #d1fae5;
  --color-background-info: #dbeafe;

  /* =======================
   ELDER WAND THEME GLASSMORPHISM
   ======================= */
  --glass-backdrop-blur: 12px;
  --glass-background-color: rgba(255, 255, 255, 0.25);
  --glass-border-color: rgba(255, 255, 255, 0.3);
  --glass-border-width: 1px;
  --glass-elevation: var(--global-elevation-02);

  /* =======================
   ELDER WAND THEME: RGB OVERRIDES
   Extract RGB values from elder wand theme semantic colors
   ======================= */
  --rgb-brand-primary: 37, 99, 235;
  /* From #2563EB */
  --rgb-brand-secondary: 3, 189, 212;
  /* From #03BDD4 */
  --rgb-brand-tertiary: 37, 99, 235;
  /* From #2563EB */
  --rgb-brand-quaternary: 3, 189, 212;
  /* From #03BDD4 */
  --rgb-brand-quinary: 67, 189, 144;
  /* From #43bd90 */
  --rgb-brand-senary: 250, 112, 154;
  /* From #fa709a */
  --rgb-violet: 124, 58, 237;
  /* From #7c3aed */
  --rgb-royal-blue: 37, 99, 235;
  /* From #2563EB */
  --rgb-cyan: 3, 189, 212;
  /* From #03BDD4 */
  --rgb-spearmint: 67, 189, 144;
  /* From #43bd90 */
  --rgb-rose: 250, 112, 154;
  /* From #fa709a */
  --rgb-white: 255, 255, 255;
  /* From #ffffff */
  --rgb-black: 0, 0, 0;
  /* From #000000 */
  --rgb-neutral-100: 243, 244, 246;
  /* From #f3f4f6 */

  /* =======================
   ELDER WAND THEME: EFFECT COLOR OVERRIDES
   Override all effect colors for proper elder wand theme adaptation
   ======================= */
  --effect-color-primary: var(--rgb-brand-primary);
  /* Blue - elder wand primary */
  --effect-color-secondary: var(--rgb-brand-secondary);
  /* Aqua - elder wand secondary */
  --effect-color-accent: var(--rgb-violet);
  /* Violet accent - keep consistent */
  --effect-color-neutral: var(--rgb-black);
  /* Black shadows work well on light bg */
  --effect-color-surface: var(--rgb-white);
  /* White glass/shimmer on light bg */

  /* =======================
   ELDER WAND THEME: PERSONALITY OVERRIDES
   Only override personality tokens that need elder wand theme adjustments
   ======================= */
  /* Most personalities use base values, only override if elder wand theme needs different intensity */
  /* Base personalities work well for elder wand theme, no overrides needed currently */

  /* =======================
   ELDER WAND THEME: SEMANTIC COMPONENT TOKENS
   Theme-aware component-specific tokens using the metaphor system
   ======================= */

  /* Glass Metaphor (Theme-Aware) */
  --surface-glass-bg: rgba(var(--effect-color-surface), 0.15);
  --surface-glass-border: rgba(var(--effect-color-surface), 0.7);
  --surface-glass-shadow: rgba(var(--effect-color-surface), 0.18);

  /* Light Metaphor (Theme-Aware) */
  --color-light-glow: rgba(var(--effect-color-primary), 0.45);
  --color-light-glow-focus: rgba(var(--effect-color-primary), 0.65);
  --color-light-shadow: rgba(var(--effect-color-accent), 0.1);
  --color-light-border: var(--color-brand-primary);
  --color-light-border-focus: var(--color-brand-secondary);

  /* Liquid Metaphor (Theme-Aware) */
  --color-liquid-shimmer-start: var(--color-brand-secondary);
  --color-liquid-shimmer-end: var(--color-brand-primary);

  /* =======================
   ELDER WAND THEME: SOPHISTICATED GLASS CHAINING
   Override glass surface colors for elder wand theme variants
   ======================= */

  /* Elder wand theme glass surface - keep default white */
  --glass-surface-color: var(--rgb-white);

  /* Elder wand theme variant glass colors */
  --glass-variant-primary: var(--rgb-brand-primary);
  --glass-variant-success: 76, 175, 80;
  --glass-variant-warning: 255, 152, 0;
  --glass-variant-danger: 244, 67, 54;
  --glass-variant-info: 33, 150, 243;

  /* Custom variant example - Add new colors here */
  --glass-variant-purple: 156, 39, 176;
  /* Custom purple variant */
  --glass-variant-emerald: 16, 185, 129;
  /* Custom emerald variant */
  --glass-variant-blue: 37, 99, 235;
  /* Elder wand blue variant */
  --glass-variant-aqua: 3, 189, 212;
  /* Elder wand aqua variant */

  /* Elder wand theme effect color adjustments */
  --effect-color-neutral: var(--rgb-black);
  /* Black shadows work on light bg */
  --effect-color-surface: var(--rgb-white);
  /* White highlights on light bg */

  /* =======================
     CONSOLE THEME: CONSOLE CARD COMPONENT COLORS
     Color variables for console card component (Red Variants)
     ======================= */

  /* Primary Colors - Red Variant */
  --console-card-bg: linear-gradient(135deg, var(--red-50) 0%, var(--purple-50) 100%);
  --console-card-primary-text: #144692;
  --console-card-primary-title: #144692;
  --console-card-primary-icon: #144692;
  --console-card-primary-border: #144692;
  

  /* Background Colors - Match Console */
  --console-card-bg-hover: #f8fafc;
  --console-card-bg-disabled: rgba(255, 255, 255, 0.95);

  /* Shadow Colors - Match Console */
  --console-card-shadow: rgba(135, 161, 151, 0.12);
  --console-card-shadow-hover: rgba(0, 0, 0, 0.2);
  --console-card-shadow-focus: rgba(47, 90, 142, 0.15);
  --console-card-shadow-active: rgba(47, 90, 142, 0.2);

  /* Interactive Colors - Red Variant */
  --console-card-button-color: #6b7280;
  --console-card-button-hover-color: var(--red-500);
  --console-card-button-focus-outline: var(--red-500);

  /* Tooltip Colors - Dark Theme */
  --console-card-tooltip-bg: #1f2937;
  --console-card-tooltip-shadow: rgba(0, 0, 0, 0.3);

  /* Loading Colors - Red Variant */
  --console-card-loading-border: #e0e0e0;
  --console-card-loading-spinner: var(--red-400);

  /* Skeleton Colors - Match Console */
  --console-card-skeleton-start: #f0f0f0;
  --console-card-skeleton-middle: #e0e0e0;
  --console-card-skeleton-end: #f0f0f0;
  --console-card-skeleton-button-start: rgba(233, 30, 99, 0.25);
  --console-card-skeleton-button-middle: rgba(212, 27, 90, 0.25);
  --console-card-skeleton-button-end: rgba(233, 30, 99, 0.25);

  /* =======================
   ELDER WAND THEME: PAGE FOOTER COMPONENT COLORS
   Color variables for page-footer component (Red Variants)
   ======================= */

  /* Background Colors */
  --page-footer-bg: linear-gradient(102.14deg, rgba(31, 41, 55, 0.8) 1.07%, rgba(31, 41, 55, 0.9) 98.01%);
  --page-footer-bg-hover: linear-gradient(102.14deg, rgba(31, 41, 55, 0.9) 1.07%, rgba(31, 41, 55, 1) 98.01%);
  --page-footer-bg-disabled: rgba(31, 41, 55, 0.6);

  /* Border Colors */
  --page-footer-border: rgba(233, 30, 99, 0.3);
  --page-footer-border-hover: rgba(233, 30, 99, 0.5);

  /* Shadow Colors */
  --page-footer-shadow: rgba(0, 0, 0, 0.1);
  --page-footer-shadow-hover: rgba(0, 0, 0, 0.2);

  /* Text Colors */
  --page-footer-text: var(--red-500);
  --page-footer-text-secondary: #9ca3af;

  /* =======================
   ELDER WAND THEME: PAGINATION COMPONENT COLORS
   Color variables for pagination component (Blue Variants - matching image)
   ======================= */

  /* Primary Colors */
  --pagination-primary-text: #374151;
  --pagination-primary-text-hover: #1f2937;
  --pagination-primary-bg: #E9EFFD;
  --pagination-primary-bg-hover: #E9EFFD;
  --pagination-primary-border: transparent;

  /* Interactive Colors */
  --pagination-button-bg: #E9EFFD;
  --pagination-button-bg-hover: #E9EFFD;
  --pagination-button-text: #374151;
  --pagination-button-text-hover: #1f2937;
  --pagination-button-border: transparent;
  --pagination-button-border-hover: transparent;

  /* Active State Colors */
  --pagination-active-bg: #9AB7F6;
  --pagination-active-text: #374151;
  --pagination-active-border: transparent;

  /* Disabled State Colors */
  --pagination-disabled-opacity: 0.5;
  --pagination-disabled-text: #9ca3af;
  --pagination-disabled-bg: #f3f4f6;

  /* Ellipsis Colors */
  --pagination-ellipsis: #374151;

  /* Focus States */
  --pagination-focus-outline: #3b82f6;
  --pagination-focus-ring: rgba(59, 130, 246, 0.2);

  /* Navigation Arrows */
  --pagination-arrow-color: #374151;
  --pagination-arrow-hover-color: #1f2937;

  /* AVA Text Cards */
  --card-background-gradient: linear-gradient(118deg, var(--red-300), var(--violet-300) 89.27%); 
  --card-box-shadow: 0 4px 12px #87a1971f;

  /* Required Field Colors */
  --agents-preview-required: #f87171;

  /* =======================
   ELDER WAND THEME: BUILD AGENTS COMPONENT COLORS
   Color variables for build-agents component (Red/Purple/Violet Variants)
   ======================= */

  /* Top Navigation Bar Colors */
  --build-agents-nav-bg: #ffffff;
  --build-agents-nav-border: #4b5563;
  --build-agents-back-btn-text: #374151;
  //--build-agents-back-btn-hover-bg: #374151;
  //--build-agents-back-btn-hover-text: var(--red-500);

  /* Agent Type Toggle Colors */
  --build-agents-toggle-bg: #f3f4f6;
  --build-agents-toggle-btn-text: #9ca3af;
  --build-agents-toggle-btn-hover: #d1d5db;
  --build-agents-toggle-active-bg: linear-gradient(103.35deg, var(--red-500) 31.33%, var(--red-600) 100%);
  --build-agents-toggle-active-text: #ffffff;
  --build-agents-toggle-shadow: rgba(0, 0, 0, 0.3);

  /* Agent Details Floater Colors */
  --build-agents-floater-bg: #ffffff;
  --build-agents-floater-border: #e5e7eb;
  --build-agents-floater-shadow: rgba(0, 0, 0, 0.1);
  --build-agents-floater-header-bg: #ffffff;
  --build-agents-floater-header-hover: #f9fafb;
  --build-agents-floater-header-border: #f3f4f6;
  --build-agents-floater-title: #1f2937;
  --build-agents-field-label: #374151;

  /* Configure Agent Panel Colors */
  --build-agents-panel-bg: #ffffff;
  --build-agents-panel-border: #f0f1f2;
  --build-agents-panel-shadow: rgba(0, 0, 0, 0.08);
  --build-agents-panel-header-bg: #ffffff;
  --build-agents-panel-header-hover: #f9fafb;
  --build-agents-panel-title: #4c515b;
  --build-agents-panel-border-light: #f3f4f6;

  /* Tab Colors */
  --build-agents-tab-bg: #f3f8fc;
  --build-agents-tab-border: #d1d5db;
  --build-agents-tab-icon: #6b7280;
  --build-agents-tab-label: #6b7280;
  --build-agents-tab-active-bg: var(--red-500);
  --build-agents-tab-active-border: var(--red-500);
  --build-agents-tab-active-icon: #ffffff;
  --build-agents-tab-active-label: #111827;

  /* Search Section Colors */
  --build-agents-search-bg: #f3f8fc;
  --build-agents-search-border: #e5e7eb;
  --build-agents-search-focus-border: var(--red-500);
  --build-agents-search-focus-shadow: rgba(233, 30, 99, 0.1);
  --build-agents-search-text: #111827;
  --build-agents-search-placeholder: #9ca3af;

  /* Tool Item Colors */
  --build-agents-tool-bg: #ffffff;
  --build-agents-tool-border: #f0f1f2;
  --build-agents-tool-shadow: rgba(0, 0, 0, 0.05);
  --build-agents-tool-shadow-alt: rgba(0, 0, 0, 0.08);
  --build-agents-tool-hover-border: #bbcff9;
  --build-agents-tool-hover-shadow: rgba(0, 0, 0, 0.1);
  --build-agents-tool-icon-bg: #e9effd;
  --build-agents-tool-icon: #ffffff;
  --build-agents-tool-name: #3b3f46;
  --build-agents-tool-count: #4c515b;
  --build-agents-tool-description: #616874;

  /* Create Tool Section Colors */
  --build-agents-create-gradient: linear-gradient(90deg, var(--red-500) 0%, var(--purple-500) 100%);
  --build-agents-create-hover-gradient: linear-gradient(90deg, var(--red-600) 0%, var(--purple-600) 100%);
  --build-agents-create-shadow: rgba(233, 30, 99, 0.3);

  /* Canvas Colors */
  --build-agents-canvas-bg: var(--background-secondary);
  --build-agents-canvas-edge: #9ca1aa;
  --build-agents-canvas-marker: #9ca1aa;
  --build-agents-canvas-arrow: #9ca1aa;
  --build-agents-canvas-node-shadow: rgba(0, 0, 0, 0.1);
  --build-agents-canvas-node-hover-shadow: rgba(0, 0, 0, 0.15);
  --build-agents-canvas-node-border: #e5e7eb;
  --build-agents-canvas-node-bg: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  --build-agents-canvas-node-hover-border: var(--red-500);
  --build-agents-canvas-node-hover-bg: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
  --build-agents-canvas-node-selected-border: var(--red-500);
  --build-agents-canvas-node-selected-bg: linear-gradient(135deg, rgba(233, 30, 99, 0.1) 0%, rgba(233, 30, 99, 0.05) 100%);
  --build-agents-canvas-node-selected-shadow: rgba(233, 30, 99, 0.1);

  /* Agent Details Dropdown Colors */
  --build-agents-dropdown-bg: #ffffff;
  --build-agents-dropdown-border: #e5e7eb;
  --build-agents-dropdown-shadow: rgba(0, 0, 0, 0.1);
  --build-agents-dropdown-hover-border: var(--red-500);
  --build-agents-dropdown-hover-shadow: rgba(233, 30, 99, 0.1);
  --build-agents-dropdown-focus-border: var(--red-500);
  --build-agents-dropdown-focus-shadow: rgba(233, 30, 99, 0.2);

  /* Execute Mode Colors */
  --build-agents-execute-border: #e5e7eb;
  --build-agents-execute-connection: var(--red-500);
  --build-agents-execute-arrow: var(--red-500);

  /* Playground Controls Colors */
  --build-agents-playground-bg: #f8f9fa;
  --build-agents-playground-border: #e9ecef;
  --build-agents-playground-title: #495057;
  --build-agents-clear-chat-bg: #dc3545;
  --build-agents-clear-chat-hover: #c82333;
  --build-agents-file-upload-bg: var(--red-500);
  --build-agents-file-upload-hover: var(--red-600);
  --build-agents-file-item-bg: #e9ecef;
  --build-agents-file-item-border: #ced4da;
  --build-agents-file-remove: #6c757d;
  --build-agents-file-remove-hover: #dc3545;

  /* Modal Colors */
  --build-agents-modal-error: #f87171;

  /* Pulsating Animation Colors */
  --build-agents-pulsating-border: #4b5563;
  --build-agents-pulsating-shadow: rgba(0, 0, 0, 0.3);
  --build-agents-pulsating-active-border: #f74646;
  --build-agents-pulsating-active-shadow: rgba(247, 70, 70, 0.5);
  --build-agents-pulsating-active-ring: rgba(247, 70, 70, 0.3);

  /* Tool Icon Colors */
  --build-agents-tool-icon-stroke: var(--purple-500);

  /* =======================
   ELDER WAND THEME: CUSTOM TABS COMPONENT COLORS
   Color variables for custom-tabs component (Red/Purple/Violet Variants)
   ======================= */

  /* Tab Item Colors */
  --custom-tabs-bg: transparent;
  --custom-tabs-border: #6b7280;
  --custom-tabs-text: #f9fafb;
  --custom-tabs-text-active: #f9fafb;

  /* Active Tab Colors */
  --custom-tabs-active-bg: var(--purple-50);
  --custom-tabs-active-border: var(--purple-500);
  --custom-tabs-active-icon: var(--purple-500);
  --custom-tabs-active-icon-filter: brightness(0) saturate(100%) invert(27%) sepia(51%) saturate(2878%) hue-rotate(330deg) brightness(97%) contrast(87%);

  /* Disabled Tab Colors */
  --custom-tabs-disabled-opacity: 0.5;

  /* Tab Icon Box Colors */
  --custom-tabs-icon-box-bg: transparent;
  --custom-tabs-icon-box-border: #d1d5db;
  --custom-tabs-icon-box-border-radius: 6px;

  /* Tab Label Colors */
  --custom-tabs-label-text: #4b5563;
  --custom-tabs-label-active-text: #4b5563;
  --custom-tabs-label-font-weight: 500;
  --custom-tabs-label-active-font-weight: 500;

  /* =======================
   ELDER WAND THEME: BUTTON GRADIENTS
   Red-to-Purple gradient for all buttons (matching the design)
   ======================= */
  
  /* Primary Button Gradient */
  --button-primary-gradient: linear-gradient(90deg, var(--red-500) 0%, var(--purple-500) 100%);
  --button-primary-gradient-hover: linear-gradient(90deg, var(--red-600) 0%, var(--purple-600) 100%);
  --button-primary-text: #ffffff;
  --button-primary-shadow: rgba(233, 30, 99, 0.3);
  --button-primary-shadow-hover: rgba(156, 39, 176, 0.4);

  /* Secondary Button Gradient */
  --button-secondary-gradient: linear-gradient(90deg, var(--red-100) 0%, var(--purple-100) 100%);
  --button-secondary-gradient-hover: linear-gradient(90deg, var(--red-200) 0%, var(--purple-200) 100%);
  --button-secondary-text: var(--red-500);
  --button-secondary-shadow: rgba(233, 30, 99, 0.2);

  /* Create Button Gradient (for + Create New Prompt style) */
  --button-create-gradient: linear-gradient(90deg, var(--red-500) 0%, var(--purple-500) 100%);
  --button-create-gradient-hover: linear-gradient(90deg, var(--red-600) 0%, var(--purple-600) 100%);
  --button-create-text: #ffffff;
  --button-create-shadow: rgba(233, 30, 99, 0.3);
}

