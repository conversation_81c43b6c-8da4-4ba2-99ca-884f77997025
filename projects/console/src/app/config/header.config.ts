import {
  HeaderConfig,
  SharedNavItem,
} from '@shared/components/app-header/app-header.component';

// Console specific navigation items
const consoleNavItems: SharedNavItem[] = [
  {
    label: 'Dashboard',
    route: '/dashboard',
    selected: false,
    hasDropdown: false,
    icon: `svgs/icons/awe_dashboard.svg`,
    disabled: true, // Dashboard link is disabled
  },
  {
    label: 'Build',
    route: '/build',
    selected: true, // Default to Build being selected since it's now the default route
    hasDropdown: true,
    dropdownOpen: false,
    icon: `svgs/icons/awe_launch.svg`,
    dropdownItems: [
      {
        label: 'Agents',
        description: 'Create, Manage and Edit Agents',
        route: '/build/agents',
        icon: `svgs/icons/awe_agents.svg`,
      },
      {
        label: 'Workflows',
        description: 'Create, Manage and Edit Workflows',
        route: '/build/workflows',
        icon: `svgs/icons/awe_workflows.svg`,
      },
    ],
  },
  {
    label: 'Libraries',
    route: '/libraries',
    selected: false,
    hasDropdown: true,
    dropdownOpen: false,
    icon: `svgs/icons/awe_libraries.svg`,
    dropdownItems: [
      {
        label: 'Prompts',
        description: 'Create, Manage and Edit Prompts',
        route: '/libraries/prompts',
        icon: `svgs/icons/awe_prompts.svg`,
      },
      {
        label: 'Models',
        description: 'Add, Manage and View Models',
        route: '/libraries/models',
        icon: `svgs/icons/awe_models.svg`,
      },
      {
        label: 'Knowledge-base',
        description: 'Add, Manage and Edit Knowledge-Base',
        route: '/libraries/knowledge-base',
        icon: `svgs/icons/awe_knowledgebase.svg`,
      },
      {
        label: 'Tools',
        description: 'Add, Manage and Edit Tools',
        route: '/libraries/tools',
        icon: `svgs/icons/awe_tools.svg`,
      },
      {
        label: 'Guardrails',
        description: 'Add, Manage and Edit Guardrails',
        route: '/libraries/guardrails',
        icon: `svgs/icons/awe_guardrails.svg`,
      },
    ],
  },
  {
    label: 'Approvals',
    route: '/approval',
    selected: false,
    hasDropdown: false,
    dropdownOpen: false,
    icon: `svgs/icons/awe_admin_ management.svg`,
    disabled: true, // Approvals link is disabled
    dropdownItems: [
      {
        label: 'Approval Agents',
        description: 'Manage approval agents',
        route: '/approval/approval-agents',
        icon: `svgs/icons/awe_agents.svg`,
      },
      {
        label: 'Approval Workflows',
        description: 'Manage approval workflows',
        route: '/approval/approval-workflows',
        icon: `svgs/icons/awe_workflows.svg`,
      },
      {
        label: 'Approval Tools',
        description: 'Manage approval tools',
        route: '/approval/approval-tools',
        icon: `svgs/icons/awe_tools.svg`,
      },
    ],
  },
  {
    label: 'User Management',
    route: '/manage',
    selected: false,
    hasDropdown: true,
    dropdownOpen: false,
    icon: `svgs/icons/awe_manage.svg`,
    dropdownItems: [
      {
        label: 'Users & Admins Management',
        description:
          'Add, Manage Users and Admins, Modify tokens and assign filters',
        route: '/manage/admin-management',
        icon: `svgs/icons/awe_admin_ management.svg`,
      },
      {
        label: 'Realm Management',
        description: 'Approve, Modify submission for Admins',
        route: '/manage/realm-management',
        icon: `svgs/icons/awe_realm_ management.svg`,
      },
    ],
  },
  {
    label: 'Analytics',
    route: '/analytics',
    selected: false,
    hasDropdown: true,
    dropdownOpen: false,
    icon: `svgs/icons/awe_analytics.svg`,
    dropdownItems: [
      {
        label: 'Agents',
        description: '',
        route: '/analytics',
        icon: `svgs/icons/awe_analytics.svg`,
      },
    ],
  },
];

// Console header configuration
export const consoleHeaderConfig: HeaderConfig = {
  logoSrc: 'assets/svgs/ascendion-logo/header-ascendion-logo.svg',
  navItems: consoleNavItems,
  showOrgSelector: true,
  showThemeToggle: false,
  showAppDrawer: true,
  showProfileDropdown: true,
  showThemeToggleInProfile: true,
  showLanguageSwitcher: true,
  availableLanguages: [
    { code: 'en', name: 'English' },
    { code: 'fil', name: 'Filipino' },
    { code: 'es', name: 'Español' },
  ],
  currentApp: 'Console',
  availableApps: [
    {
      name: 'Console',
      route: '/console',
      icon: 'assets/svgs/ascendion-logo/header-ascendion-logo.svg',
      description: 'Agent & Workflow Management',
    },
    {
      name: 'Experience Studio',
      route: '/experience-studio',
      icon: 'assets/svgs/ascendion-logo/header-ascendion-logo.svg',
      description: 'UI/UX Design & Prototyping',
    },
    {
      name: 'Product Studio',
      route: '/product-studio',
      icon: 'assets/svgs/ascendion-logo/header-ascendion-logo.svg',
      description: 'Product Development',
    },
    {
      name: 'Launchpad',
      route: '/launchpad',
      icon: 'assets/svgs/ascendion-logo/header-ascendion-logo.svg',
      description: 'Project Launch Hub',
    },
  ],
  projectName: 'Console',
  redirectUrl: '/build/agents',
  // Logo animation configuration
  enableLogoAnimation: true,
  logoAnimationInterval: 4000, // 4 seconds between transitions
  logoAnimationStyle: 'fade',
  studioLogos: ['ascendion.svg', 'AAVA_logo.svg', 'CONSOLE_LOGO.svg'],
  studioNames: [
    'Console Studio',
    'Experience Studio',
    'Product Studio',
    'Launchpad',
  ],
};
